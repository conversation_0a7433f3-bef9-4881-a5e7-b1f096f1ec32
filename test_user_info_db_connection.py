#!/usr/bin/env python3
"""
测试用户信息插件的数据库连接问题
"""

import sys
import traceback
from utils.config_utils import load_config
from db_plugins.mysql import MySQLDB


def test_basic_connection():
    """测试基本数据库连接"""
    print("🔍 测试基本数据库连接...")
    
    try:
        config = load_config()
        db_config = config.get("mysql", {})
        
        if not db_config:
            print("❌ 未找到数据库配置")
            return False
        
        print(f"📋 数据库配置: {db_config}")
        
        db = MySQLDB(**db_config)
        db.connect()
        
        # 检查连接状态
        if db.connection and db.connection.is_connected():
            print("✅ 数据库连接成功")
            
            # 测试简单查询
            cursor = db.connection.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            print(f"✅ 测试查询成功: {result}")
            
            return True
        else:
            print("❌ 数据库连接失败")
            return False
        
    except Exception as e:
        print(f"❌ 数据库连接测试失败: {e}")
        traceback.print_exc()
        return False


def test_user_info_plugin_functions():
    """测试用户信息插件的SQL函数"""
    print("\n🔍 测试用户信息插件SQL函数...")
    
    try:
        config = load_config()
        db_config = config.get("mysql", {})
        
        db = MySQLDB(**db_config)
        db.connect()
        
        # 检查连接状态
        if not db.connection or not db.connection.is_connected():
            print("❌ 数据库连接不可用")
            return False
        
        print("✅ 数据库连接正常")
        
        # 注册用户信息插件的SQL函数
        print("📝 注册用户信息插件SQL函数...")
        from plugins.user_info.sql import USER_INFO_SQL_FUNCTIONS
        db.register_plugin_functions("TestUserInfo", USER_INFO_SQL_FUNCTIONS)
        print("✅ SQL函数注册成功")
        
        # 测试数据库表初始化
        print("🏗️ 测试数据库表初始化...")
        db.init_user_data_tables()
        print("✅ 数据库表初始化成功")
        
        # 测试记录程序状态
        print("📊 测试记录程序状态...")
        db.record_program_status('startup')
        print("✅ 程序状态记录成功")
        
        # 测试获取配置
        print("⚙️ 测试获取用户数据配置...")
        config_data = db.get_user_data_config()
        if config_data:
            print(f"✅ 配置获取成功: {config_data}")
        else:
            print("⚠️ 未找到配置数据，但这是正常的")
        
        # 清理
        db.unregister_plugin_functions("TestUserInfo")
        print("🧹 清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 用户信息插件测试失败: {e}")
        traceback.print_exc()
        return False


def test_connection_availability():
    """测试连接可用性检查"""
    print("\n🔍 测试连接可用性检查...")
    
    try:
        config = load_config()
        db_config = config.get("mysql", {})
        
        db = MySQLDB(**db_config)
        db.connect()
        
        # 模拟用户信息插件的连接检查逻辑
        print("🔍 检查 self.connection 是否存在...")
        if hasattr(db, 'connection'):
            print(f"✅ db.connection 存在: {db.connection}")
        else:
            print("❌ db.connection 不存在")
            return False
        
        print("🔍 检查连接是否有效...")
        if db.connection is None:
            print("❌ db.connection 为 None")
            return False
        
        print("🔍 检查连接是否已连接...")
        if not db.connection.is_connected():
            print("❌ 数据库连接未建立")
            return False
        
        print("✅ 连接可用性检查通过")
        
        # 测试在SQL函数中访问连接
        print("🔍 测试在SQL函数中访问连接...")
        cursor = db.connection.cursor()
        cursor.execute("SELECT CONNECTION_ID()")
        connection_id = cursor.fetchone()[0]
        print(f"✅ 当前连接ID: {connection_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ 连接可用性测试失败: {e}")
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 用户信息插件数据库连接测试")
    print("=" * 60)
    
    # 测试基本连接
    if not test_basic_connection():
        print("\n❌ 基本连接测试失败，停止后续测试")
        return
    
    # 测试连接可用性
    if not test_connection_availability():
        print("\n❌ 连接可用性测试失败，停止后续测试")
        return
    
    # 测试插件函数
    if not test_user_info_plugin_functions():
        print("\n❌ 插件函数测试失败")
        return
    
    print("\n" + "=" * 60)
    print("✅ 所有测试通过！")
    print("=" * 60)


if __name__ == "__main__":
    main()
