#!/usr/bin/env python3
"""
人工服务插件增强分析功能演示
展示优化后的历史记录获取和智能分析能力
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timedelta
from typing import Dict, List
import json

class MockHumanHandoverPlugin:
    """模拟人工服务插件，演示增强功能"""
    
    def __init__(self):
        self.mock_data = self._generate_mock_data()
    
    def _generate_mock_data(self):
        """生成模拟数据"""
        return {
            'users': {
                '张三': {
                    'conversation_history': [
                        {
                            'sender': '张三',
                            'message_content': '你好，我想了解一下产品功能',
                            'created_at': datetime.now() - timedelta(days=2),
                            'message_type': 'user'
                        },
                        {
                            'sender': 'AI助手',
                            'message_content': '您好！很高兴为您服务，请问您想了解哪个产品呢？',
                            'created_at': datetime.now() - timedelta(days=2),
                            'message_type': 'bot'
                        },
                        {
                            'sender': '张三',
                            'message_content': '我买的VIP会员功能用不了',
                            'created_at': datetime.now() - timedelta(hours=3),
                            'message_type': 'user'
                        },
                        {
                            'sender': '张三',
                            'message_content': '我要投诉，这个问题一直没解决',
                            'created_at': datetime.now(),
                            'message_type': 'user'
                        }
                    ],
                    'interaction_patterns': {
                        'total_messages': 68,
                        'active_days': 12,
                        'avg_messages_per_day': 5.7,
                        'most_active_hour': 15,
                        'message_types': {'user': 45, 'bot': 23}
                    },
                    'historical_issues': [
                        {
                            'id': 1,
                            'trigger_keyword': '咨询',
                            'trigger_message': '会员功能怎么使用',
                            'handover_time': datetime.now() - timedelta(days=20),
                            'status': 'resolved',
                            'context_summary': '用户咨询VIP会员功能使用方法，已提供详细说明'
                        },
                        {
                            'id': 2,
                            'trigger_keyword': '故障',
                            'trigger_message': 'VIP功能无法使用',
                            'handover_time': datetime.now() - timedelta(days=5),
                            'status': 'pending',
                            'context_summary': '用户反馈VIP会员功能故障，技术部门正在处理'
                        }
                    ]
                },
                '李四': {
                    'conversation_history': [
                        {
                            'sender': '李四',
                            'message_content': '退款申请怎么提交',
                            'created_at': datetime.now() - timedelta(hours=1),
                            'message_type': 'user'
                        },
                        {
                            'sender': '李四',
                            'message_content': '我要退款，产品不符合预期',
                            'created_at': datetime.now(),
                            'message_type': 'user'
                        }
                    ],
                    'interaction_patterns': {
                        'total_messages': 8,
                        'active_days': 2,
                        'avg_messages_per_day': 4.0,
                        'most_active_hour': 10,
                        'message_types': {'user': 6, 'bot': 2}
                    },
                    'historical_issues': []
                }
            }
        }
    
    def analyze_user_behavior(self, patterns: Dict) -> str:
        """分析用户行为模式"""
        behavior_traits = []
        
        total_messages = patterns.get('total_messages', 0)
        avg_messages = patterns.get('avg_messages_per_day', 0)
        most_active_hour = patterns.get('most_active_hour')
        
        # 活跃度分析
        if total_messages > 50:
            behavior_traits.append("高频用户")
        elif total_messages > 10:
            behavior_traits.append("活跃用户")
        else:
            behavior_traits.append("新用户")
        
        # 互动频率分析
        if avg_messages > 8:
            behavior_traits.append("互动频繁")
        elif avg_messages > 3:
            behavior_traits.append("正常互动")
        else:
            behavior_traits.append("偶尔互动")
        
        # 活跃时间分析
        if most_active_hour is not None:
            if 9 <= most_active_hour <= 17:
                behavior_traits.append("工作时间活跃")
            elif 18 <= most_active_hour <= 22:
                behavior_traits.append("晚间活跃")
            else:
                behavior_traits.append("非常规时间活跃")
        
        return "，".join(behavior_traits)
    
    def analyze_historical_issues(self, historical_issues: List[Dict]) -> str:
        """分析历史问题模式"""
        if not historical_issues:
            return "首次转人工用户"
        
        issue_patterns = []
        issue_count = len(historical_issues)
        
        # 问题频率分析
        if issue_count > 5:
            issue_patterns.append("频繁转人工用户")
        elif issue_count > 2:
            issue_patterns.append("偶有问题用户")
        else:
            issue_patterns.append("少量转人工记录")
        
        # 问题类型分析
        keywords = [issue.get('trigger_keyword', '') for issue in historical_issues]
        keyword_counts = {}
        for keyword in keywords:
            keyword_counts[keyword] = keyword_counts.get(keyword, 0) + 1
        
        if keyword_counts:
            most_common = max(keyword_counts.items(), key=lambda x: x[1])
            if most_common[1] > 1:
                issue_patterns.append(f"常见问题类型: {most_common[0]}")
        
        # 处理状态分析
        statuses = [issue.get('status', '') for issue in historical_issues]
        pending_count = statuses.count('pending')
        if pending_count > 0:
            issue_patterns.append(f"有{pending_count}个待处理问题")
        
        return "，".join(issue_patterns)
    
    def generate_handling_recommendations(self, trigger_message: str, 
                                        interaction_patterns: Dict, 
                                        historical_issues: List[Dict]) -> str:
        """生成处理建议"""
        recommendations = []
        
        # 基于历史问题的建议
        if historical_issues:
            if len(historical_issues) > 3:
                recommendations.append("重点用户，需优先处理")
            
            pending_issues = [issue for issue in historical_issues if issue.get('status') == 'pending']
            if pending_issues:
                recommendations.append("存在未解决问题，需要跟进")
        
        # 基于用户活跃度的建议
        if interaction_patterns.get('total_messages', 0) > 30:
            recommendations.append("活跃用户，建议快速响应")
        
        # 基于触发关键词的建议
        urgent_keywords = ['投诉', '退款', '故障', '紧急']
        if any(keyword in trigger_message for keyword in urgent_keywords):
            recommendations.append("问题紧急，需立即处理")
        
        return "，".join(recommendations) if recommendations else "按标准流程处理"
    
    def enhanced_context_analysis(self, user_name: str) -> str:
        """增强的上下文分析"""
        user_data = self.mock_data['users'].get(user_name)
        if not user_data:
            return f"用户 {user_name} 数据不存在"
        
        conversation_history = user_data['conversation_history']
        interaction_patterns = user_data['interaction_patterns']
        historical_issues = user_data['historical_issues']
        
        # 获取最新的触发消息
        trigger_message = conversation_history[-1]['message_content'] if conversation_history else ""
        
        analysis_parts = []
        
        # 1. 基础问题分析
        analysis_parts.append(f"当前问题: {trigger_message}")
        
        # 2. 用户行为模式分析
        behavior_analysis = self.analyze_user_behavior(interaction_patterns)
        analysis_parts.append(f"用户特征: {behavior_analysis}")
        
        # 3. 历史问题模式分析
        history_analysis = self.analyze_historical_issues(historical_issues)
        analysis_parts.append(f"历史模式: {history_analysis}")
        
        # 4. 综合建议
        recommendations = self.generate_handling_recommendations(
            trigger_message, interaction_patterns, historical_issues
        )
        analysis_parts.append(f"处理建议: {recommendations}")
        
        return "；".join(analysis_parts) + "。"

def demo_enhanced_analysis():
    """演示增强分析功能"""
    print("🚀 人工服务插件增强分析功能演示")
    print("=" * 60)
    
    plugin = MockHumanHandoverPlugin()
    
    # 演示用户1：张三（老用户，有历史问题）
    print("\n📊 用户分析案例1：张三")
    print("-" * 40)
    
    user_data = plugin.mock_data['users']['张三']
    print(f"对话历史: {len(user_data['conversation_history'])} 条")
    print(f"总消息数: {user_data['interaction_patterns']['total_messages']}")
    print(f"活跃天数: {user_data['interaction_patterns']['active_days']}")
    print(f"历史问题: {len(user_data['historical_issues'])} 个")
    
    analysis = plugin.enhanced_context_analysis('张三')
    print(f"\n🔍 增强分析结果:")
    print(analysis)
    
    # 演示用户2：李四（新用户，首次转人工）
    print("\n📊 用户分析案例2：李四")
    print("-" * 40)
    
    user_data = plugin.mock_data['users']['李四']
    print(f"对话历史: {len(user_data['conversation_history'])} 条")
    print(f"总消息数: {user_data['interaction_patterns']['total_messages']}")
    print(f"活跃天数: {user_data['interaction_patterns']['active_days']}")
    print(f"历史问题: {len(user_data['historical_issues'])} 个")
    
    analysis = plugin.enhanced_context_analysis('李四')
    print(f"\n🔍 增强分析结果:")
    print(analysis)
    
    # 对比分析
    print("\n📈 对比分析")
    print("-" * 40)
    print("张三 vs 李四:")
    print("• 张三是高频老用户，有未解决的历史问题，需要优先处理")
    print("• 李四是新用户，首次转人工，但涉及退款需要及时响应")
    print("• 两者都需要快速处理，但张三的优先级更高")

def demo_data_structure():
    """演示数据结构"""
    print("\n📋 数据结构演示")
    print("=" * 60)
    
    # 用户互动模式示例
    print("\n🔄 用户互动模式:")
    interaction_example = {
        'total_messages': 68,
        'active_days': 12,
        'avg_messages_per_day': 5.7,
        'most_active_hour': 15,
        'message_types': {'user': 45, 'bot': 23}
    }
    print(json.dumps(interaction_example, indent=2, ensure_ascii=False))
    
    # 历史问题记录示例
    print("\n📝 历史问题记录:")
    issue_example = {
        'id': 1,
        'trigger_keyword': '投诉',
        'trigger_message': '产品有问题',
        'handover_time': '2024-01-15 14:30:00',
        'status': 'pending',
        'context_summary': '用户投诉产品质量问题，需要技术部门跟进'
    }
    print(json.dumps(issue_example, indent=2, ensure_ascii=False))

def main():
    """主演示函数"""
    demo_enhanced_analysis()
    demo_data_structure()
    
    print("\n" + "=" * 60)
    print("✅ 演示完成！")
    
    print("\n🎯 优化效果总结:")
    print("1. 📈 更全面的用户画像分析")
    print("2. 🔍 深度的历史问题模式识别") 
    print("3. 🎯 智能的处理优先级判断")
    print("4. 💡 个性化的处理建议生成")
    print("5. ⚡ 提升人工服务效率和质量")

if __name__ == "__main__":
    main()
