#!/usr/bin/env python3
"""
最终清理脚本 - 清理所有测试任务
"""

import sys
import traceback
from utils.config_utils import load_config
from db_plugins.mysql import MySQLDB


def final_cleanup():
    """最终清理所有测试任务"""
    print("🧹 最终清理所有测试任务")
    print("=" * 60)
    
    try:
        # 连接数据库
        config = load_config()
        db_config = config.get("mysql", {})
        db = MySQLDB(**db_config)
        db.connect()
        
        cursor = db.connection.cursor(dictionary=True)
        
        # 1. 查看所有任务
        print("📋 当前所有任务:")
        cursor.execute("""
            SELECT id, task_name, schedule_type, enabled, send_count, next_send_at
            FROM scheduled_messages 
            ORDER BY task_name
        """)
        
        tasks = cursor.fetchall()
        test_tasks = []
        
        for task in tasks:
            status = "🟢 启用" if task['enabled'] else "🔴 禁用"
            next_send = task['next_send_at'] or "无"
            print(f"  - {task['task_name']} ({task['schedule_type']}) {status} 发送{task['send_count']}次")
            
            # 识别测试任务
            task_name_lower = task['task_name'].lower()
            if any(keyword in task_name_lower for keyword in ['测试', 'test', '修复', '验证']):
                test_tasks.append(task)
        
        # 2. 删除测试任务
        if test_tasks:
            print(f"\n🗑️ 发现 {len(test_tasks)} 个测试任务，准备删除:")
            for task in test_tasks:
                print(f"  - {task['task_name']}")
            
            confirm = input("\n确认删除这些测试任务吗？(y/N): ").strip().lower()
            if confirm == 'y':
                deleted_count = 0
                for task in test_tasks:
                    try:
                        # 删除相关的历史记录
                        cursor.execute("""
                            DELETE FROM scheduled_message_history 
                            WHERE task_id = %s
                        """, (task['id'],))
                        
                        # 删除相关的内容记录
                        cursor.execute("""
                            DELETE FROM scheduled_message_contents 
                            WHERE message_id = %s
                        """, (task['id'],))
                        
                        # 删除任务本身
                        cursor.execute("""
                            DELETE FROM scheduled_messages 
                            WHERE id = %s
                        """, (task['id'],))
                        
                        deleted_count += 1
                        print(f"✅ 删除任务: {task['task_name']}")
                        
                    except Exception as e:
                        print(f"❌ 删除任务失败 {task['task_name']}: {e}")
                
                db.connection.commit()
                print(f"\n✅ 成功删除 {deleted_count} 个测试任务")
            else:
                print("❌ 取消删除操作")
        else:
            print("\n✅ 没有发现测试任务")
        
        # 3. 显示最终状态
        print("\n📋 清理后的任务状态:")
        cursor.execute("""
            SELECT task_name, schedule_type, enabled, send_count, next_send_at
            FROM scheduled_messages 
            ORDER BY task_name
        """)
        
        remaining_tasks = cursor.fetchall()
        if remaining_tasks:
            for task in remaining_tasks:
                status = "🟢 启用" if task['enabled'] else "🔴 禁用"
                next_send = task['next_send_at'] or "无"
                print(f"  - {task['task_name']} ({task['schedule_type']}) {status} 下次:{next_send}")
        else:
            print("  ✅ 没有剩余任务")
        
        # 4. 检查待发送任务
        print("\n📋 待发送任务:")
        cursor.execute("""
            SELECT task_name, chat_name, schedule_type, next_send_at
            FROM scheduled_messages 
            WHERE enabled = TRUE 
            AND (next_send_at IS NULL OR next_send_at <= NOW())
            ORDER BY next_send_at
        """)
        
        pending = cursor.fetchall()
        if pending:
            print(f"⚠️ 仍有 {len(pending)} 个待发送任务:")
            for task in pending:
                print(f"  - {task['task_name']} -> {task['chat_name']} ({task['schedule_type']})")
        else:
            print("✅ 没有待发送任务")
        
        return True
        
    except Exception as e:
        print(f"❌ 清理失败: {e}")
        traceback.print_exc()
        return False


def create_normal_task_example():
    """创建一个正常的任务示例"""
    print("\n💡 是否要创建一个正常的定时任务示例？")
    create = input("创建示例任务吗？(y/N): ").strip().lower()
    
    if create != 'y':
        return
    
    try:
        # 连接数据库
        config = load_config()
        db_config = config.get("mysql", {})
        db = MySQLDB(**db_config)
        db.connect()
        
        # 注册SQL函数
        from plugins.scheduled_message.sql import SCHEDULED_MESSAGE_SQL_FUNCTIONS
        db.register_plugin_functions("ExampleTask", SCHEDULED_MESSAGE_SQL_FUNCTIONS)
        
        from datetime import time
        
        # 创建一个每日早安消息
        success = db.add_scheduled_message(
            task_name="每日早安",
            chat_name="文件传输助手",
            chat_type="private",
            message_type="text",
            message_content="🌅 早上好！新的一天开始了，祝您工作顺利！",
            schedule_type="daily",
            schedule_time=time(8, 0)
        )
        
        if success:
            print("✅ 创建示例任务成功: 每日早安 (每天8:00发送)")
        else:
            print("❌ 创建示例任务失败")
        
        # 卸载SQL函数
        db.unregister_plugin_functions("ExampleTask")
        
    except Exception as e:
        print(f"❌ 创建示例任务失败: {e}")


def main():
    """主函数"""
    print("🧹 定时消息最终清理工具")
    print("=" * 60)
    
    if final_cleanup():
        create_normal_task_example()
        
        print("\n🎉 清理完成！")
        print("\n💡 接下来的建议:")
        print("1. 🔄 重启微信机器人程序")
        print("2. 📋 使用 simple_message_manager.py 管理定时任务")
        print("3. ⚠️ 观察确认不再有重复发送")
        print("4. 📝 根据需要添加新的定时任务")
        
        print("\n🚀 现在定时消息功能应该完全正常了！")
        return True
    else:
        print("\n❌ 清理失败")
        return False


if __name__ == "__main__":
    main()
