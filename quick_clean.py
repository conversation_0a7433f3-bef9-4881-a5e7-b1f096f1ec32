#!/usr/bin/env python3
"""
快速清理剩余的测试任务
"""

from utils.config_utils import load_config
from db_plugins.mysql import MySQLDB

def quick_clean():
    # 连接数据库
    config = load_config()
    db_config = config.get("mysql", {})
    db = MySQLDB(**db_config)
    db.connect()

    cursor = db.connection.cursor()

    # 删除"吃饭提醒"任务
    cursor.execute("DELETE FROM scheduled_message_history WHERE task_id IN (SELECT id FROM scheduled_messages WHERE task_name = '吃饭提醒')")
    cursor.execute("DELETE FROM scheduled_message_contents WHERE message_id IN (SELECT id FROM scheduled_messages WHERE task_name = '吃饭提醒')")
    cursor.execute("DELETE FROM scheduled_messages WHERE task_name = '吃饭提醒'")

    # 删除"每日早安"任务（如果不需要的话）
    cursor.execute("DELETE FROM scheduled_message_history WHERE task_id IN (SELECT id FROM scheduled_messages WHERE task_name = '每日早安')")
    cursor.execute("DELETE FROM scheduled_message_contents WHERE message_id IN (SELECT id FROM scheduled_messages WHERE task_name = '每日早安')")
    cursor.execute("DELETE FROM scheduled_messages WHERE task_name = '每日早安'")

    # 删除"测试"任务
    cursor.execute("DELETE FROM scheduled_message_history WHERE task_id IN (SELECT id FROM scheduled_messages WHERE task_name = '测试')")
    cursor.execute("DELETE FROM scheduled_message_contents WHERE message_id IN (SELECT id FROM scheduled_messages WHERE task_name = '测试')")
    cursor.execute("DELETE FROM scheduled_messages WHERE task_name = '测试'")

    db.connection.commit()

    # 查看剩余任务
    cursor.execute("SELECT task_name, schedule_type, enabled FROM scheduled_messages")
    tasks = cursor.fetchall()

    print(f"✅ 清理完成！剩余任务数量: {len(tasks)}")
    for task in tasks:
        print(f"  - {task[0]} ({task[1]}) {'启用' if task[2] else '禁用'}")

if __name__ == "__main__":
    quick_clean()
