#!/usr/bin/env python3
"""
测试增强版定时消息插件
"""

import sys
import traceback
from datetime import datetime, time
from utils.config_utils import load_config
from db_plugins.mysql import MySQLDB


def test_enhanced_scheduled_message():
    """测试增强版定时消息功能"""
    print("🧪 测试增强版定时消息功能")
    print("=" * 60)
    
    try:
        # 连接数据库
        config = load_config()
        db_config = config.get("mysql", {})
        db = MySQLDB(**db_config)
        db.connect()
        
        # 注册SQL函数
        from plugins.scheduled_message.sql import SCHEDULED_MESSAGE_SQL_FUNCTIONS
        db.register_plugin_functions("TestScheduledMessage", SCHEDULED_MESSAGE_SQL_FUNCTIONS)
        
        # 初始化表
        print("🏗️ 初始化数据库表...")
        db.init_scheduled_message_tables()
        print("✅ 数据库表初始化成功")
        
        # 测试添加文本消息
        print("\n📝 测试添加文本消息...")
        success = db.add_scheduled_message(
            task_name="测试文本消息",
            chat_name="测试群",
            chat_type="group",
            message_type="text",
            message_content="这是一条测试文本消息",
            schedule_type="daily",
            schedule_time=time(9, 0)
        )
        print(f"✅ 文本消息添加: {'成功' if success else '失败'}")
        
        # 测试添加文件消息
        print("\n📁 测试添加文件消息...")
        file_contents = [
            {
                'type': 'file',
                'paths': ['D:/test/image1.jpg', 'D:/test/document.pdf'],
                'delay': 0
            }
        ]
        success = db.add_scheduled_message(
            task_name="测试文件消息",
            chat_name="测试群",
            chat_type="group",
            message_type="file",
            schedule_type="daily",
            schedule_time=time(10, 0),
            contents=file_contents
        )
        print(f"✅ 文件消息添加: {'成功' if success else '失败'}")
        
        # 测试添加表情消息
        print("\n😊 测试添加表情消息...")
        emotion_contents = [
            {
                'type': 'emotion',
                'index': 25,
                'delay': 0
            }
        ]
        success = db.add_scheduled_message(
            task_name="测试表情消息",
            chat_name="测试群",
            chat_type="group",
            message_type="emotion",
            schedule_type="daily",
            schedule_time=time(11, 0),
            contents=emotion_contents
        )
        print(f"✅ 表情消息添加: {'成功' if success else '失败'}")
        
        # 测试添加链接卡片消息
        print("\n🔗 测试添加链接卡片消息...")
        url_contents = [
            {
                'type': 'url_card',
                'url': 'https://docs.wxauto.org',
                'delay': 0
            }
        ]
        success = db.add_scheduled_message(
            task_name="测试链接卡片",
            chat_name="测试群",
            chat_type="group",
            message_type="url_card",
            schedule_type="daily",
            schedule_time=time(12, 0),
            contents=url_contents
        )
        print(f"✅ 链接卡片添加: {'成功' if success else '失败'}")
        
        # 测试添加混合消息
        print("\n🎭 测试添加混合消息...")
        mixed_contents = [
            {
                'type': 'text',
                'content': '大家好！我要发送一些文件和表情',
                'delay': 0
            },
            {
                'type': 'file',
                'paths': ['D:/test/report.pdf'],
                'delay': 1
            },
            {
                'type': 'emotion',
                'index': 10,
                'delay': 0.5
            },
            {
                'type': 'url_card',
                'url': 'https://example.com',
                'delay': 1
            }
        ]
        success = db.add_scheduled_message(
            task_name="测试混合消息",
            chat_name="测试群",
            chat_type="group",
            message_type="mixed",
            schedule_type="daily",
            schedule_time=time(14, 0),
            contents=mixed_contents
        )
        print(f"✅ 混合消息添加: {'成功' if success else '失败'}")
        
        # 测试获取所有消息
        print("\n📋 测试获取所有定时消息...")
        messages = db.get_all_scheduled_messages()
        print(f"✅ 获取到 {len(messages)} 条定时消息")
        
        for msg in messages:
            print(f"  - {msg['task_name']} ({msg['message_type']}) -> {msg['chat_name']}")
        
        # 测试获取消息内容
        print("\n📄 测试获取消息内容...")
        for msg in messages:
            if msg['message_type'] in ['file', 'emotion', 'url_card', 'mixed']:
                contents = db.get_message_contents(msg['id'])
                print(f"  消息 '{msg['task_name']}' 有 {len(contents)} 个内容项:")
                for content in contents:
                    print(f"    - {content['content_type']}: {content}")
        
        # 测试获取待发送消息
        print("\n⏰ 测试获取待发送消息...")
        pending = db.get_pending_messages()
        print(f"✅ 获取到 {len(pending)} 条待发送消息")
        
        # 清理测试数据
        print("\n🧹 清理测试数据...")
        test_tasks = ["测试文本消息", "测试文件消息", "测试表情消息", "测试链接卡片", "测试混合消息"]
        for task_name in test_tasks:
            try:
                db.delete_scheduled_message(task_name)
                print(f"✅ 删除测试任务: {task_name}")
            except Exception as e:
                print(f"⚠️ 删除任务失败 {task_name}: {e}")
        
        # 卸载SQL函数
        db.unregister_plugin_functions("TestScheduledMessage")
        print("✅ SQL函数已卸载")
        
        print("\n🎉 所有测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        traceback.print_exc()
        return False


def test_plugin_integration():
    """测试插件集成"""
    print("\n🔌 测试插件集成")
    print("=" * 60)
    
    try:
        # 模拟handler
        class MockHandler:
            def __init__(self):
                config = load_config()
                db_config = config.get("mysql", {})
                self.db = MySQLDB(**db_config)
                self.db.connect()
                self.wx = None  # 模拟微信实例
                
            def _log(self, msg, level="INFO"):
                print(f"[MockHandler] [{level}] {msg}")
        
        handler = MockHandler()
        
        # 导入并实例化插件
        from plugins.scheduled_message.plugin import ScheduledMessagePlugin
        plugin = ScheduledMessagePlugin(handler)
        
        print("✅ 插件实例化成功")
        
        # 测试添加各种类型的消息
        print("\n📝 测试插件接口...")
        
        # 添加文本消息
        success = plugin.add_daily_message(
            "插件测试文本",
            "测试群",
            "group",
            "这是通过插件添加的文本消息",
            "09:00"
        )
        print(f"✅ 插件添加文本消息: {'成功' if success else '失败'}")
        
        # 添加文件消息
        success = plugin.add_file_message(
            "插件测试文件",
            "测试群",
            "group",
            ["D:/test/file1.txt", "D:/test/file2.jpg"],
            "daily",
            schedule_time=time(10, 0)
        )
        print(f"✅ 插件添加文件消息: {'成功' if success else '失败'}")
        
        # 添加表情消息
        success = plugin.add_emotion_message(
            "插件测试表情",
            "测试群",
            "group",
            15,
            "daily",
            schedule_time=time(11, 0)
        )
        print(f"✅ 插件添加表情消息: {'成功' if success else '失败'}")
        
        # 添加链接卡片消息
        success = plugin.add_url_card_message(
            "插件测试链接",
            "测试群",
            "group",
            "https://github.com",
            "daily",
            schedule_time=time(12, 0)
        )
        print(f"✅ 插件添加链接消息: {'成功' if success else '失败'}")
        
        # 添加混合消息
        mixed_contents = [
            {'type': 'text', 'content': '混合消息测试', 'delay': 0},
            {'type': 'emotion', 'index': 20, 'delay': 1},
            {'type': 'url_card', 'url': 'https://example.com', 'delay': 1}
        ]
        success = plugin.add_mixed_message(
            "插件测试混合",
            "测试群",
            "group",
            mixed_contents,
            "daily",
            schedule_time=time(13, 0)
        )
        print(f"✅ 插件添加混合消息: {'成功' if success else '失败'}")
        
        # 获取任务列表
        tasks = plugin.get_all_tasks()
        print(f"✅ 获取到 {len(tasks)} 个任务")
        
        # 清理测试数据
        test_tasks = ["插件测试文本", "插件测试文件", "插件测试表情", "插件测试链接", "插件测试混合"]
        for task_name in test_tasks:
            plugin.delete_task(task_name)
        
        print("✅ 插件集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 插件集成测试失败: {e}")
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🧪 增强版定时消息插件测试")
    print("=" * 60)
    
    success_count = 0
    total_tests = 2
    
    # 测试数据库功能
    if test_enhanced_scheduled_message():
        success_count += 1
    
    # 测试插件集成
    if test_plugin_integration():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！增强版定时消息插件功能正常")
        print("\n💡 新功能说明:")
        print("- ✅ 支持文本消息定时发送")
        print("- ✅ 支持文件/图片/视频定时发送")
        print("- ✅ 支持表情包定时发送")
        print("- ✅ 支持链接卡片定时发送")
        print("- ✅ 支持混合消息类型组合发送")
        print("- ✅ 支持发送延迟控制")
        print("- ✅ 完整的任务管理功能")
        print("- ✅ 详细的发送历史记录")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return False


if __name__ == "__main__":
    main()
