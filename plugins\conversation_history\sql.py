"""
Conversation History Plugin SQL Functions
包含对话记录插件所需的所有SQL查询函数
"""
from typing import Optional, List, Dict, Tuple
from datetime import datetime, timedelta


def init_conversation_tables(self):
    """初始化对话记录相关表"""
    cursor = self.connection.cursor()

    # 创建对话记录表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS conversation_history (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            chat_name VARCHAR(255) NOT NULL COMMENT '聊天窗口名称',
            chat_type ENUM('group', 'private') NOT NULL COMMENT '聊天类型',
            sender VARCHAR(255) NOT NULL COMMENT '发送者',
            message_content TEXT NOT NULL COMMENT '消息内容',
            message_type ENUM('user', 'bot') NOT NULL DEFAULT 'user' COMMENT '消息类型',
            session_id VARCHAR(64) NOT NULL COMMENT '会话ID',
            conversation_id VARCHAR(64) NULL COMMENT '对话ID，用于关联用户消息和AI回复',
            reply_to_id BIGINT NULL COMMENT '回复的消息ID，用于建立回复关系',
            message_batch_id VARCHAR(64) NULL COMMENT '消息批次ID，用于标识同一批次的消息',
            is_batch_start BOOLEAN DEFAULT FALSE COMMENT '是否为批次开始',
            is_batch_end BOOLEAN DEFAULT FALSE COMMENT '是否为批次结束',
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            INDEX idx_chat_session (chat_name, session_id),
            INDEX idx_created_at (created_at),
            INDEX idx_chat_type (chat_type),
            INDEX idx_sender (sender),
            INDEX idx_conversation_id (conversation_id),
            INDEX idx_reply_to_id (reply_to_id),
            INDEX idx_message_batch_id (message_batch_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """)

    # 创建会话配置表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS conversation_config (
            id INT AUTO_INCREMENT PRIMARY KEY,
            chat_name VARCHAR(255) NOT NULL COMMENT '聊天窗口名称',
            chat_type ENUM('group', 'private') NOT NULL COMMENT '聊天类型',
            enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用对话记录',
            context_limit INT DEFAULT 100 COMMENT '上下文条数限制',
            session_hours INT DEFAULT 24 COMMENT '会话时长（小时）',
            ai_agent_id BIGINT NULL COMMENT '关联的AI智能体ID',
            trigger_keywords TEXT NULL COMMENT '触发关键词，JSON格式',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY uk_chat (chat_name, chat_type),
            INDEX idx_enabled (enabled)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """)

    try:
        self.connection.commit()
    except Exception as e:
        self.connection.rollback()
        raise e


def generate_session_id(self, chat_name: str, chat_type: str, session_hours: int = 24) -> str:
    """生成会话ID，基于聊天名称和时间窗口"""
    import hashlib
    from datetime import datetime

    # 计算时间窗口
    now = datetime.now()
    time_window = now.replace(minute=0, second=0, microsecond=0)
    time_window = time_window - timedelta(hours=time_window.hour % session_hours)

    # 生成会话ID
    session_key = f"{chat_name}_{chat_type}_{time_window.strftime('%Y%m%d_%H')}"
    session_id = hashlib.md5(session_key.encode()).hexdigest()[:16]

    return session_id


def save_message(self, chat_name: str, chat_type: str, sender: str,
                message_content: str, message_type: str = 'user', session_hours: int = 24,
                conversation_id: str = None, reply_to_id: int = None,
                message_batch_id: str = None, is_batch_start: bool = False,
                is_batch_end: bool = False):
    """保存消息到对话记录（增强版）"""
    cursor = self.connection.cursor()

    # 生成会话ID
    session_id = self.generate_session_id(chat_name, chat_type, session_hours)

    cursor.execute("""
        INSERT INTO conversation_history
        (chat_name, chat_type, sender, message_content, message_type, session_id,
         conversation_id, reply_to_id, message_batch_id, is_batch_start, is_batch_end)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    """, (chat_name, chat_type, sender, message_content, message_type, session_id,
          conversation_id, reply_to_id, message_batch_id, is_batch_start, is_batch_end))

    try:
        self.connection.commit()
        return cursor.lastrowid  # 返回插入的记录ID
    except Exception as e:
        self.connection.rollback()
        raise e


def get_conversation_context(self, chat_name: str, chat_type: str,
                           limit: int = 100, session_hours: int = 24) -> List[Dict]:
    """获取对话上下文"""
    cursor = self.connection.cursor(dictionary=True)

    # 生成当前会话ID
    session_id = self.generate_session_id(chat_name, chat_type, session_hours)

    cursor.execute("""
        SELECT sender, message_content, message_type, created_at
        FROM conversation_history
        WHERE chat_name = %s AND chat_type = %s AND session_id = %s
        ORDER BY created_at DESC
        LIMIT %s
    """, (chat_name, chat_type, session_id, limit))

    results = cursor.fetchall()
    # 返回时按时间正序排列
    return list(reversed(results))


def get_user_conversation_context(self, chat_name: str, chat_type: str, sender: str,
                                limit: int = 100, session_hours: int = 24) -> List[Dict]:
    """获取特定用户的对话上下文（包括该用户的消息和AI回复）"""
    cursor = self.connection.cursor(dictionary=True)

    # 生成当前会话ID
    session_id = self.generate_session_id(chat_name, chat_type, session_hours)

    cursor.execute("""
        SELECT sender, message_content, message_type, created_at
        FROM conversation_history
        WHERE chat_name = %s AND chat_type = %s AND session_id = %s
        AND (sender = %s OR message_type = 'bot')
        ORDER BY created_at DESC
        LIMIT %s
    """, (chat_name, chat_type, session_id, sender, limit))

    results = cursor.fetchall()
    # 返回时按时间正序排列
    return list(reversed(results))


def get_conversation_config(self, chat_name: str, chat_type: str) -> Optional[Dict]:
    """获取对话配置"""
    cursor = self.connection.cursor(dictionary=True)

    cursor.execute("""
        SELECT * FROM conversation_config
        WHERE chat_name = %s AND chat_type = %s
    """, (chat_name, chat_type))

    return cursor.fetchone()


def upsert_conversation_config(self, chat_name: str, chat_type: str,
                              enabled: bool = True, context_limit: int = 100,
                              session_hours: int = 24, ai_agent_id: int = None,
                              trigger_keywords: str = None):
    """插入或更新对话配置"""
    cursor = self.connection.cursor()

    cursor.execute("""
        INSERT INTO conversation_config
        (chat_name, chat_type, enabled, context_limit, session_hours, ai_agent_id, trigger_keywords)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
            enabled = VALUES(enabled),
            context_limit = VALUES(context_limit),
            session_hours = VALUES(session_hours),
            ai_agent_id = VALUES(ai_agent_id),
            trigger_keywords = VALUES(trigger_keywords),
            updated_at = CURRENT_TIMESTAMP
    """, (chat_name, chat_type, enabled, context_limit, session_hours, ai_agent_id, trigger_keywords))

    try:
        self.connection.commit()
    except Exception as e:
        self.connection.rollback()
        raise e


def get_ai_agent_by_id(self, agent_id: int) -> Optional[Dict]:
    """根据ID获取AI智能体配置"""
    cursor = self.connection.cursor(dictionary=True)

    cursor.execute("""
        SELECT * FROM ai_agent_profiles
        WHERE id = %s
    """, (agent_id,))

    return cursor.fetchone()


def cleanup_old_conversations(self, days_to_keep: int = 30):
    """清理旧的对话记录"""
    cursor = self.connection.cursor()

    cutoff_date = datetime.now() - timedelta(days=days_to_keep)

    cursor.execute("""
        DELETE FROM conversation_history
        WHERE created_at < %s
    """, (cutoff_date,))

    deleted_count = cursor.rowcount

    try:
        self.connection.commit()
        return deleted_count
    except Exception as e:
        self.connection.rollback()
        raise e


def get_conversation_with_replies(self, chat_name: str, chat_type: str,
                                 limit: int = 50, session_hours: int = 24) -> List[Dict]:
    """获取包含回复关系的对话记录"""
    cursor = self.connection.cursor(dictionary=True)

    # 生成当前会话ID
    session_id = self.generate_session_id(chat_name, chat_type, session_hours)

    cursor.execute("""
        SELECT
            ch.*,
            reply_to.sender as reply_to_sender,
            reply_to.message_content as reply_to_content,
            reply_to.message_type as reply_to_type
        FROM conversation_history ch
        LEFT JOIN conversation_history reply_to ON ch.reply_to_id = reply_to.id
        WHERE ch.chat_name = %s AND ch.chat_type = %s AND ch.session_id = %s
        ORDER BY ch.created_at DESC
        LIMIT %s
    """, (chat_name, chat_type, session_id, limit))

    results = cursor.fetchall()
    return list(reversed(results))


def get_conversation_by_batch(self, chat_name: str, chat_type: str,
                             message_batch_id: str) -> List[Dict]:
    """根据批次ID获取对话记录"""
    cursor = self.connection.cursor(dictionary=True)

    cursor.execute("""
        SELECT * FROM conversation_history
        WHERE chat_name = %s AND chat_type = %s AND message_batch_id = %s
        ORDER BY created_at ASC
    """, (chat_name, chat_type, message_batch_id))

    return cursor.fetchall()


def get_user_message_batches(self, chat_name: str, chat_type: str, sender: str,
                           limit: int = 10, session_hours: int = 24) -> List[Dict]:
    """获取用户的消息批次信息"""
    cursor = self.connection.cursor(dictionary=True)

    # 生成当前会话ID
    session_id = self.generate_session_id(chat_name, chat_type, session_hours)

    cursor.execute("""
        SELECT
            message_batch_id,
            COUNT(*) as message_count,
            MIN(created_at) as batch_start_time,
            MAX(created_at) as batch_end_time,
            GROUP_CONCAT(message_content ORDER BY created_at SEPARATOR ' ') as combined_content
        FROM conversation_history
        WHERE chat_name = %s AND chat_type = %s AND session_id = %s
        AND sender = %s AND message_batch_id IS NOT NULL
        GROUP BY message_batch_id
        ORDER BY batch_start_time DESC
        LIMIT %s
    """, (chat_name, chat_type, session_id, sender, limit))

    return cursor.fetchall()


def save_message_batch(self, chat_name: str, chat_type: str, sender: str,
                      messages: List[str], message_type: str = 'user',
                      session_hours: int = 24, conversation_id: str = None) -> Tuple[str, List[int]]:
    """批量保存消息并返回批次ID和消息ID列表"""
    import uuid

    if not messages:
        return None, []

    # 生成批次ID
    batch_id = str(uuid.uuid4())
    message_ids = []

    for i, message_content in enumerate(messages):
        is_start = (i == 0)
        is_end = (i == len(messages) - 1)

        message_id = self.save_message(
            chat_name=chat_name,
            chat_type=chat_type,
            sender=sender,
            message_content=message_content,
            message_type=message_type,
            session_hours=session_hours,
            conversation_id=conversation_id,
            message_batch_id=batch_id,
            is_batch_start=is_start,
            is_batch_end=is_end
        )
        message_ids.append(message_id)

    return batch_id, message_ids


def save_ai_reply_with_context(self, chat_name: str, chat_type: str,
                              ai_response: str, user_message_ids: List[int],
                              session_hours: int = 24, conversation_id: str = None) -> int:
    """保存AI回复并建立与用户消息的关联"""
    # 如果有多个用户消息，回复最后一条
    reply_to_id = user_message_ids[-1] if user_message_ids else None

    # 保存AI回复
    ai_message_id = self.save_message(
        chat_name=chat_name,
        chat_type=chat_type,
        sender="AI助手",
        message_content=ai_response,
        message_type='bot',
        session_hours=session_hours,
        conversation_id=conversation_id,
        reply_to_id=reply_to_id
    )

    return ai_message_id


# 导出SQL函数字典
CONVERSATION_HISTORY_SQL_FUNCTIONS = {
    'init_conversation_tables': init_conversation_tables,
    'generate_session_id': generate_session_id,
    'save_message': save_message,
    'get_conversation_context': get_conversation_context,
    'get_user_conversation_context': get_user_conversation_context,
    'get_conversation_config': get_conversation_config,
    'upsert_conversation_config': upsert_conversation_config,
    'get_ai_agent_by_id': get_ai_agent_by_id,
    'cleanup_old_conversations': cleanup_old_conversations,
    'get_conversation_with_replies': get_conversation_with_replies,
    'get_conversation_by_batch': get_conversation_by_batch,
    'get_user_message_batches': get_user_message_batches,
    'save_message_batch': save_message_batch,
    'save_ai_reply_with_context': save_ai_reply_with_context,
}
