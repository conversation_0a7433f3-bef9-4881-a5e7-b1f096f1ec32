-- 对话历史记录表结构迁移SQL脚本
-- 添加新的字段以支持增强的消息关联功能

-- 检查并添加 conversation_id 字段
SET @column_exists = (
    SELECT COUNT(*) 
    FROM information_schema.columns 
    WHERE table_schema = DATABASE() 
    AND table_name = 'conversation_history' 
    AND column_name = 'conversation_id'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE conversation_history ADD COLUMN conversation_id VARCHAR(64) NULL COMMENT "对话ID，用于关联用户消息和AI回复"',
    'SELECT "conversation_id column already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加 reply_to_id 字段
SET @column_exists = (
    SELECT COUNT(*) 
    FROM information_schema.columns 
    WHERE table_schema = DATABASE() 
    AND table_name = 'conversation_history' 
    AND column_name = 'reply_to_id'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE conversation_history ADD COLUMN reply_to_id BIGINT NULL COMMENT "回复的消息ID，用于建立回复关系"',
    'SELECT "reply_to_id column already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加 message_batch_id 字段
SET @column_exists = (
    SELECT COUNT(*) 
    FROM information_schema.columns 
    WHERE table_schema = DATABASE() 
    AND table_name = 'conversation_history' 
    AND column_name = 'message_batch_id'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE conversation_history ADD COLUMN message_batch_id VARCHAR(64) NULL COMMENT "消息批次ID，用于标识同一批次的消息"',
    'SELECT "message_batch_id column already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加 is_batch_start 字段
SET @column_exists = (
    SELECT COUNT(*) 
    FROM information_schema.columns 
    WHERE table_schema = DATABASE() 
    AND table_name = 'conversation_history' 
    AND column_name = 'is_batch_start'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE conversation_history ADD COLUMN is_batch_start BOOLEAN DEFAULT FALSE COMMENT "是否为批次开始"',
    'SELECT "is_batch_start column already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加 is_batch_end 字段
SET @column_exists = (
    SELECT COUNT(*) 
    FROM information_schema.columns 
    WHERE table_schema = DATABASE() 
    AND table_name = 'conversation_history' 
    AND column_name = 'is_batch_end'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE conversation_history ADD COLUMN is_batch_end BOOLEAN DEFAULT FALSE COMMENT "是否为批次结束"',
    'SELECT "is_batch_end column already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加索引（如果不存在）
-- conversation_id 索引
SET @index_exists = (
    SELECT COUNT(*) 
    FROM information_schema.statistics 
    WHERE table_schema = DATABASE() 
    AND table_name = 'conversation_history' 
    AND index_name = 'idx_conversation_id'
);

SET @sql = IF(@index_exists = 0, 
    'ALTER TABLE conversation_history ADD INDEX idx_conversation_id (conversation_id)',
    'SELECT "idx_conversation_id index already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- reply_to_id 索引
SET @index_exists = (
    SELECT COUNT(*) 
    FROM information_schema.statistics 
    WHERE table_schema = DATABASE() 
    AND table_name = 'conversation_history' 
    AND index_name = 'idx_reply_to_id'
);

SET @sql = IF(@index_exists = 0, 
    'ALTER TABLE conversation_history ADD INDEX idx_reply_to_id (reply_to_id)',
    'SELECT "idx_reply_to_id index already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- message_batch_id 索引
SET @index_exists = (
    SELECT COUNT(*) 
    FROM information_schema.statistics 
    WHERE table_schema = DATABASE() 
    AND table_name = 'conversation_history' 
    AND index_name = 'idx_message_batch_id'
);

SET @sql = IF(@index_exists = 0, 
    'ALTER TABLE conversation_history ADD INDEX idx_message_batch_id (message_batch_id)',
    'SELECT "idx_message_batch_id index already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 显示最终的表结构
SELECT '=== 迁移完成，当前表结构 ===' as message;
DESCRIBE conversation_history;

-- 显示索引信息
SELECT '=== 索引信息 ===' as message;
SHOW INDEX FROM conversation_history;

SELECT '=== 迁移完成 ===' as message;
