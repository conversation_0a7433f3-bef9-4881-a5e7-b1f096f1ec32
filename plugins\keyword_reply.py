import os
import json
import uuid
from typing import List, Optional
from datetime import datetime

from . import Plugin


class KeywordReplyPlugin(Plugin):
    """
    关键词回复插件：根据配置文件中的关键词自动回复
    支持：
    - 全局启用/禁用
    - 每个聊天窗口单独启用/禁用
    默认优先级：10
    """

    priority = 10

    def __init__(self, handler):
        super().__init__(handler)

        # 加载关键词规则配置文件
        config_path = os.path.join(os.path.dirname(__file__), "config", "keyword_rules.json")
        with open(config_path, "r", encoding="utf-8") as f:
            self.config = json.load(f)

        self.enabled = self.config.get("enabled", True)  # 全局开关

    def _match_rules(self, rules, messages, chat, is_global=False):
        """原始的规则匹配方法（保持兼容性）"""
        for msg in reversed(messages):
            for keyword, reply in rules.items():
                if keyword in msg:
                    source = "全局" if is_global else "局部"
                    self.handler._log(f"[关键词回复] 【{chat}】{source}匹配 '{keyword}'，回复：{reply}")
                    return reply
        return None

    def _match_rules_enhanced(self, rules, messages, chat, chat_type, is_global=False):
        """
        增强的规则匹配方法
        支持对话历史记录存储和消息关联
        """
        for msg in reversed(messages):
            for keyword, reply in rules.items():
                if keyword in msg:
                    source = "全局" if is_global else "局部"
                    self.handler._log(f"[关键词回复] 【{chat}】【{chat_type}】{source}匹配 '{keyword}'，回复：{reply}")

                    # 存储用户消息和AI回复到对话历史
                    self._save_conversation_with_reply(chat, chat_type, messages, reply, keyword)

                    return reply
        return None

    def on_messages(self, chat: str, messages: List[str]) -> Optional[str]:
        """
        处理消息并返回关键词回复（增强版）
        支持增强的对话历史记录存储
        """
        if not self.enabled:
            self.handler._log("[KeywordReplyPlugin] 全局禁用，不处理消息")
            return None  # 全局禁用

        chat_config = self.config["chat_specific_rules"].get(chat, {})
        chat_enabled = chat_config.get("enabled", True)
        use_global_rules = chat_config.get("use_global_rules", False)
        chat_rules = chat_config.get("rules", {})
        chat_type = chat_config.get("type", "group")  # 默认为群聊

        global_enabled = self.config.get("enabled", True)

        if not chat_enabled:
            if use_global_rules and global_enabled:
                self.handler._log(f"[KeywordReplyPlugin] 【{chat}】局部禁用，但允许使用全局规则")
                chat_rules = {}
            else:
                self.handler._log(f"[KeywordReplyPlugin] 【{chat}】局部禁用且不允许使用全局规则，跳过处理")
                return None

        # 尝试局部规则（使用增强方法）
        reply = self._match_rules_enhanced(chat_rules, messages, chat, chat_type, is_global=False)
        if reply:
            return reply

        # 尝试全局规则（仅当允许时）
        if use_global_rules and global_enabled:
            global_rules = self.config.get("default_rule", {})
            reply = self._match_rules_enhanced(global_rules, messages, chat, chat_type, is_global=True)
            if reply:
                return reply

        return None

    def _save_conversation_with_reply(self, chat: str, chat_type: str,
                                    user_messages: List[str], ai_reply: str,
                                    matched_keyword: str):
        """
        保存用户消息和AI回复到对话历史记录
        使用增强的存储逻辑
        """
        try:
            # 获取对话历史插件
            conversation_plugin = self._get_conversation_history_plugin()
            if not conversation_plugin:
                self.handler._log("[关键词回复] 未找到对话历史插件，跳过历史记录存储", level="WARNING")
                return

            # 生成对话ID用于关联这次交互
            conversation_id = str(uuid.uuid4())

            # 根据聊天类型决定存储策略
            user_message_ids = []

            if chat_type == 'group':
                # 群聊：如果有多条消息，作为批次存储
                if len(user_messages) > 1:
                    _, message_ids = conversation_plugin.db.save_message_batch(
                        chat_name=chat,
                        chat_type=chat_type,
                        sender='用户',
                        messages=user_messages,
                        message_type='user',
                        conversation_id=conversation_id
                    )
                    user_message_ids = message_ids
                else:
                    # 单条消息
                    message_id = conversation_plugin.db.save_message(
                        chat_name=chat,
                        chat_type=chat_type,
                        sender='用户',
                        message_content=user_messages[0],
                        message_type='user',
                        conversation_id=conversation_id
                    )
                    user_message_ids = [message_id]
            else:
                # 私聊：每条消息单独存储
                for msg in user_messages:
                    message_id = conversation_plugin.db.save_message(
                        chat_name=chat,
                        chat_type=chat_type,
                        sender='用户',
                        message_content=msg,
                        message_type='user',
                        conversation_id=conversation_id
                    )
                    user_message_ids.append(message_id)

            # 保存AI回复并建立关联
            ai_reply_with_context = f"[关键词回复] 匹配关键词: {matched_keyword}\n{ai_reply}"
            ai_message_id = conversation_plugin.db.save_ai_reply_with_context(
                chat_name=chat,
                chat_type=chat_type,
                ai_response=ai_reply_with_context,
                user_message_ids=user_message_ids,
                conversation_id=conversation_id
            )

            self.handler._log(f"[关键词回复] 已保存对话记录，关联消息数: {len(user_message_ids)}，AI回复ID: {ai_message_id}")

        except Exception as e:
            self.handler._log(f"[关键词回复] 保存对话历史失败: {e}", level="ERROR")

    def _get_conversation_history_plugin(self):
        """获取对话历史插件实例"""
        try:
            # 从handler的插件列表中查找对话历史插件
            for plugin in getattr(self.handler, 'plugins', []):
                if plugin.__class__.__name__ == 'ConversationHistoryPlugin':
                    return plugin
            return None
        except Exception as e:
            self.handler._log(f"[关键词回复] 获取对话历史插件失败: {e}", level="ERROR")
            return None