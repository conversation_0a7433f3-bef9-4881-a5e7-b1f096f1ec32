#!/usr/bin/env python3
"""
测试修复后的定时消息功能
"""

import sys
import traceback
from datetime import datetime, time, timedelta
from utils.config_utils import load_config
from db_plugins.mysql import MySQLDB


def test_fixed_functionality():
    """测试修复后的功能"""
    print("🧪 测试修复后的定时消息功能")
    print("=" * 60)
    
    try:
        # 连接数据库
        config = load_config()
        db_config = config.get("mysql", {})
        db = MySQLDB(**db_config)
        db.connect()
        
        # 注册SQL函数
        from plugins.scheduled_message.sql import SCHEDULED_MESSAGE_SQL_FUNCTIONS
        db.register_plugin_functions("TestFixed", SCHEDULED_MESSAGE_SQL_FUNCTIONS)
        
        # 1. 测试添加文本消息
        print("\n📝 测试添加文本消息...")
        success = db.add_scheduled_message(
            task_name="修复测试_文本",
            chat_name="测试群",
            chat_type="group",
            message_type="text",
            message_content="这是修复后的文本消息测试",
            schedule_type="daily",
            schedule_time=time(9, 0)
        )
        print(f"✅ 文本消息添加: {'成功' if success else '失败'}")
        
        # 2. 测试添加混合消息
        print("\n🎭 测试添加混合消息...")
        mixed_contents = [
            {'type': 'text', 'content': '修复测试开始', 'delay': 0},
            {'type': 'emotion', 'index': 15, 'delay': 1},
            {'type': 'text', 'content': '修复测试结束', 'delay': 1}
        ]
        success = db.add_scheduled_message(
            task_name="修复测试_混合",
            chat_name="测试群",
            chat_type="group",
            message_type="mixed",
            schedule_type="daily",
            schedule_time=time(10, 0),
            contents=mixed_contents
        )
        print(f"✅ 混合消息添加: {'成功' if success else '失败'}")
        
        # 3. 测试获取消息
        print("\n📋 测试获取消息...")
        messages = db.get_all_scheduled_messages()
        print(f"✅ 获取到 {len(messages)} 条消息")
        
        # 找到我们的测试消息
        test_messages = [msg for msg in messages if msg['task_name'].startswith('修复测试_')]
        for msg in test_messages:
            print(f"  - {msg['task_name']} ({msg['message_type']}) -> {msg['chat_name']}")
            
            # 如果是混合消息，获取内容详情
            if msg['message_type'] == 'mixed':
                contents = db.get_message_contents(msg['id'])
                print(f"    包含 {len(contents)} 个内容项:")
                for content in contents:
                    print(f"      {content['content_order']}: {content['content_type']}")
        
        # 4. 测试发送状态更新
        print("\n📤 测试发送状态更新...")
        if test_messages:
            test_task = test_messages[0]
            try:
                db.update_message_sent(test_task['id'], success=True)
                print("✅ 发送状态更新成功")
            except Exception as e:
                print(f"❌ 发送状态更新失败: {e}")
        
        # 5. 测试获取发送历史
        print("\n📊 测试获取发送历史...")
        history = db.get_message_history(limit=5)
        print(f"✅ 获取到 {len(history)} 条历史记录")
        for record in history[-3:]:  # 显示最近3条
            print(f"  - {record['sent_at']}: {record.get('task_name', 'Unknown')} -> {record['status']}")
        
        # 6. 清理测试数据
        print("\n🧹 清理测试数据...")
        for msg in test_messages:
            try:
                db.delete_scheduled_message(msg['task_name'])
                print(f"✅ 删除测试任务: {msg['task_name']}")
            except Exception as e:
                print(f"⚠️ 删除任务失败 {msg['task_name']}: {e}")
        
        # 卸载SQL函数
        db.unregister_plugin_functions("TestFixed")
        
        print("\n🎉 所有测试通过！修复成功")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        traceback.print_exc()
        return False


def test_plugin_integration():
    """测试插件集成"""
    print("\n🔌 测试插件集成")
    print("=" * 60)
    
    try:
        # 模拟handler
        class MockHandler:
            def __init__(self):
                config = load_config()
                db_config = config.get("mysql", {})
                self.db = MySQLDB(**db_config)
                self.db.connect()
                self.wx = None  # 模拟微信实例
                
            def _log(self, msg, level="INFO"):
                print(f"[MockHandler] [{level}] {msg}")
        
        handler = MockHandler()
        
        # 导入并实例化插件
        from plugins.scheduled_message.plugin import ScheduledMessagePlugin
        plugin = ScheduledMessagePlugin(handler)
        
        print("✅ 插件实例化成功")
        
        # 测试添加文本消息
        success = plugin.add_daily_message(
            "插件修复测试",
            "测试群",
            "group",
            "这是插件修复后的测试消息",
            "09:00"
        )
        print(f"✅ 插件添加文本消息: {'成功' if success else '失败'}")
        
        # 测试添加混合消息
        mixed_contents = [
            {'type': 'text', 'content': '插件混合消息测试', 'delay': 0},
            {'type': 'emotion', 'index': 20, 'delay': 1}
        ]
        success = plugin.add_mixed_message(
            "插件混合测试",
            "测试群",
            "group",
            mixed_contents,
            "daily",
            schedule_time=time(10, 0)
        )
        print(f"✅ 插件添加混合消息: {'成功' if success else '失败'}")
        
        # 获取任务列表
        tasks = plugin.get_all_tasks()
        test_tasks = [task for task in tasks if task['task_name'].startswith('插件')]
        print(f"✅ 获取到 {len(test_tasks)} 个插件测试任务")
        
        # 清理测试数据
        for task in test_tasks:
            plugin.delete_task(task['task_name'])
        
        print("✅ 插件集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 插件集成测试失败: {e}")
        traceback.print_exc()
        return False


def create_immediate_test_task():
    """创建一个立即可以测试的任务"""
    print("\n⚡ 创建立即测试任务")
    print("=" * 60)
    
    try:
        # 连接数据库
        config = load_config()
        db_config = config.get("mysql", {})
        db = MySQLDB(**db_config)
        db.connect()
        
        # 注册SQL函数
        from plugins.scheduled_message.sql import SCHEDULED_MESSAGE_SQL_FUNCTIONS
        db.register_plugin_functions("ImmediateTest", SCHEDULED_MESSAGE_SQL_FUNCTIONS)
        
        # 创建一个1分钟后发送的任务
        send_time = datetime.now() + timedelta(minutes=1)
        
        success = db.add_scheduled_message(
            task_name="立即测试_修复验证",
            chat_name="文件传输助手",
            chat_type="private",
            message_type="text",
            message_content="🎉 定时消息功能修复成功！这是一条测试消息。",
            schedule_type="once",
            schedule_date=send_time.date(),
            schedule_time=send_time.time()
        )
        
        if success:
            print(f"✅ 立即测试任务创建成功")
            print(f"📅 发送时间: {send_time}")
            print(f"📱 发送目标: 文件传输助手")
            print(f"💬 消息内容: 定时消息功能修复成功测试")
            
            # 检查待发送消息
            pending = db.get_pending_messages()
            immediate_tasks = [msg for msg in pending if msg['task_name'] == "立即测试_修复验证"]
            
            if immediate_tasks:
                print("✅ 任务已进入待发送队列")
                print("💡 请观察1分钟后是否收到测试消息")
            else:
                print("⚠️ 任务未进入待发送队列，可能时间设置有问题")
        else:
            print("❌ 立即测试任务创建失败")
        
        # 卸载SQL函数
        db.unregister_plugin_functions("ImmediateTest")
        
        return success
        
    except Exception as e:
        print(f"❌ 创建立即测试任务失败: {e}")
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🧪 定时消息功能修复验证测试")
    print("=" * 60)
    
    success_count = 0
    total_tests = 3
    
    # 测试基本功能
    if test_fixed_functionality():
        success_count += 1
    
    # 测试插件集成
    if test_plugin_integration():
        success_count += 1
    
    # 创建立即测试任务
    if create_immediate_test_task():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！定时消息功能修复成功")
        print("\n💡 修复内容:")
        print("- ✅ 修复了 'Unknown column message_content' 错误")
        print("- ✅ 更新了数据库表结构")
        print("- ✅ 迁移了现有数据")
        print("- ✅ 优化了发送历史记录")
        print("- ✅ 增强了错误处理")
        
        print("\n🚀 现在可以正常使用以下功能:")
        print("- 📝 文本消息定时发送")
        print("- 📁 文件/图片/视频定时发送")
        print("- 😊 表情包定时发送")
        print("- 🔗 链接卡片定时发送")
        print("- 🎭 混合消息定时发送")
        
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return False


if __name__ == "__main__":
    main()
