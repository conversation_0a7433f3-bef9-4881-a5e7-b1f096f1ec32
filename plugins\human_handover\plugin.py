"""
私聊转人工插件
支持关键词触发、AI总结上下文、用户标签管理和自动加入黑名单
"""

import os
import json
import threading
from datetime import datetime, timedelta, time
from typing import List, Optional, Dict, Any

from core.plugin_base import Plugin
from .sql import HUMAN_HANDOVER_SQL_FUNCTIONS
from utils.deepseek_client import DeepSeekClient
from utils.config_utils import load_config


class HumanHandoverPlugin(Plugin):
    """
    私聊转人工插件
    - 关键词触发转人工
    - AI总结用户上下文
    - 自动生成用户标签
    - 导出到文本文件
    - 自动加入黑名单
    """

    priority = 10  # 高优先级，仅次于黑名单插件

    def __init__(self, handler):
        super().__init__(handler)

        # 使用主处理程序提供的数据库连接
        self.db = getattr(handler, 'db', None)
        if not self.db:
            raise ValueError("❌ handler 未绑定数据库实例，无法继续")

        # 注册本插件所需的 SQL 函数
        self.db.register_plugin_functions("HumanHandoverPlugin", HUMAN_HANDOVER_SQL_FUNCTIONS)

        # 初始化数据库表
        try:
            self.db.init_human_handover_tables()
            self.handler._log("✅ 转人工相关表初始化完成")
        except Exception as e:
            self.handler._log(f"❌ 初始化转人工表失败: {e}", level="ERROR")

        # 加载触发关键词
        self.trigger_keywords = self._load_trigger_keywords()

        # 加载工作时间配置
        self._load_work_schedule_config()

        # 创建输出目录
        self.output_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "handover_reports")
        os.makedirs(self.output_dir, exist_ok=True)

        # 线程锁
        self.lock = threading.Lock()

        # 初始化DeepSeek客户端
        self.deepseek_client = None
        try:
            # 从配置中获取DeepSeek API密钥
            from utils.config_utils import load_config
            config = load_config()
            deepseek_api_key = config.get('deepseek', {}).get('api_key')
            if deepseek_api_key:
                self.deepseek_client = DeepSeekClient(deepseek_api_key)
                self.handler._log("✅ DeepSeek客户端初始化成功")
            else:
                self.handler._log("⚠️ 未配置DeepSeek API密钥，将使用基础分析功能", level="WARNING")
        except Exception as e:
            self.handler._log(f"❌ DeepSeek客户端初始化失败: {e}", level="ERROR")

        self.handler._log("✅ 私聊转人工插件已启动")

    def __del__(self):
        """插件析构时卸载SQL函数"""
        if hasattr(self, 'db') and self.db:
            self.db.unregister_plugin_functions("HumanHandoverPlugin")

    def _load_trigger_keywords(self) -> List[str]:
        """加载触发关键词"""
        try:
            keywords_data = self.db.get_handover_keywords()
            keywords = [item['keyword'] for item in keywords_data]
            self.handler._log(f"📋 加载了 {len(keywords)} 个转人工触发关键词: {keywords}")
            return keywords
        except Exception as e:
            self.handler._log(f"❌ 加载触发关键词失败: {e}", level="ERROR")
            return ['转人工', '人工客服', '联系客服', '投诉', '退款']  # 默认关键词

    def _load_work_schedule_config(self):
        """从数据库加载工作时间配置"""
        try:
            # 从数据库获取配置
            config = self.db.get_work_schedule_config()

            # 工作时间配置
            self.work_schedule_enabled = config.get('enabled', True)
            self.work_days = config.get('work_days', [1, 2, 3, 4, 5])  # 周一到周五
            self.work_start = config.get('work_start', '09:00')
            self.work_end = config.get('work_end', '18:00')
            self.timezone = config.get('timezone', 'Asia/Shanghai')
            self.holiday_mode = config.get('holiday_mode', False)

            # 响应消息配置
            self.work_hours_response = config.get('work_hours_response', '您好！我已为您转接人工客服，客服人员会尽快为您处理。')
            self.non_work_hours_response = config.get('non_work_hours_response', '您好！现在是非工作时间，您的问题已记录，客服人员会在工作时间内优先为您处理。')
            self.weekend_response = config.get('weekend_response', '您好！现在是周末时间，您的问题已记录，客服人员会在下个工作日优先为您处理。')
            self.holiday_response = config.get('holiday_response', '您好！现在是节假日期间，您的问题已记录，客服人员会在工作日恢复后优先为您处理。')

            self.handler._log(f"📅 工作时间配置已从数据库加载: 工作日{self.work_days}, 时间{self.work_start}-{self.work_end}")

        except Exception as e:
            self.handler._log(f"❌ 从数据库加载工作时间配置失败: {e}", level="ERROR")
            # 设置默认值
            self.work_schedule_enabled = True
            self.work_days = [1, 2, 3, 4, 5]
            self.work_start = '09:00'
            self.work_end = '18:00'
            self.timezone = 'Asia/Shanghai'
            self.holiday_mode = False
            self.work_hours_response = '您好！我已为您转接人工客服，客服人员会尽快为您处理。'
            self.non_work_hours_response = '您好！现在是非工作时间，您的问题已记录，客服人员会在工作时间内优先为您处理。'
            self.weekend_response = '您好！现在是周末时间，您的问题已记录，客服人员会在下个工作日优先为您处理。'
            self.holiday_response = '您好！现在是节假日期间，您的问题已记录，客服人员会在工作日恢复后优先为您处理。'

    def _is_work_time(self) -> tuple[bool, str]:
        """
        判断当前是否为工作时间

        Returns:
            tuple: (是否为工作时间, 时间类型描述)
        """
        if not self.work_schedule_enabled:
            return True, 'work_hours'

        try:
            now = datetime.now()

            # 如果是节假日模式
            if self.holiday_mode:
                return False, 'holiday'

            # 检查是否为工作日
            weekday = now.isoweekday()  # 1=周一, 7=周日
            if weekday not in self.work_days:
                return False, 'weekend'

            # 检查是否在工作时间内
            current_time = now.time()

            # 解析工作开始和结束时间
            start_hour, start_minute = map(int, self.work_start.split(':'))
            end_hour, end_minute = map(int, self.work_end.split(':'))

            work_start_time = time(start_hour, start_minute)
            work_end_time = time(end_hour, end_minute)

            if work_start_time <= current_time <= work_end_time:
                return True, 'work_hours'
            else:
                return False, 'non_work_hours'

        except Exception as e:
            self.handler._log(f"❌ 判断工作时间失败: {e}", level="ERROR")
            # 出错时默认为工作时间
            return True, 'work_hours'

    def on_messages(self, chat: str, messages: List[str]) -> Optional[str]:
        """
        检查是否触发转人工关键词
        只处理私聊消息
        """
        # 获取聊天类型
        chat_type = getattr(self.handler, 'chat_type_cache', {}).get(chat, 'friend')

        # 只处理私聊
        if chat_type != 'friend':
            return None

        # 检查是否包含触发关键词
        for message in messages:
            for keyword in self.trigger_keywords:
                if keyword in message:
                    self.handler._log(f"🚨 检测到转人工关键词 '{keyword}' 来自用户 '{chat}'")

                    # 异步处理转人工流程
                    threading.Thread(
                        target=self._handle_handover,
                        args=(chat, keyword, message),
                        daemon=True
                    ).start()

                    # 返回转人工提示消息
                    return self._get_handover_response(keyword)

        return None

    def _get_handover_response(self, keyword: str) -> str:
        """获取转人工响应消息，根据工作时间返回不同内容"""
        is_work_time, time_type = self._is_work_time()

        # 根据时间类型返回相应的响应消息
        if time_type == 'work_hours':
            return self.work_hours_response
        elif time_type == 'non_work_hours':
            return self.non_work_hours_response
        elif time_type == 'weekend':
            return self.weekend_response
        elif time_type == 'holiday':
            return self.holiday_response
        else:
            # 默认响应
            return self.work_hours_response

    def _handle_handover(self, user_name: str, trigger_keyword: str, trigger_message: str):
        """处理转人工流程"""
        try:
            with self.lock:
                self.handler._log(f"🔄 开始处理用户 '{user_name}' 的转人工请求")

                # 1. 获取用户对话历史
                conversation_history = self._get_user_conversation_history(user_name)

                # 2. AI总结上下文
                context_summary = self._generate_context_summary(user_name, conversation_history, trigger_message)

                # 3. 生成用户标签
                user_tags = self._generate_user_tags(user_name, conversation_history, trigger_message, context_summary)

                # 4. 保存转人工记录
                record_id = self.db.create_handover_record(
                    user_name=user_name,
                    trigger_keyword=trigger_keyword,
                    trigger_message=trigger_message,
                    context_summary=context_summary,
                    user_tags=user_tags,
                    conversation_history=conversation_history,
                    auto_blacklisted=True
                )

                # 5. 导出到文本文件
                self._export_handover_report(record_id, user_name, trigger_keyword,
                                           trigger_message, context_summary, user_tags, conversation_history)

                # 6. 自动加入黑名单
                self._add_to_blacklist(user_name)

                self.handler._log(f"✅ 用户 '{user_name}' 转人工处理完成，记录ID: {record_id}")

        except Exception as e:
            self.handler._log(f"❌ 处理转人工失败: {e}", level="ERROR")
            import traceback
            self.handler._log(f"错误详情: {traceback.format_exc()}", level="ERROR")

    def _get_user_conversation_history(self, user_name: str, limit: int = 30) -> List[Dict]:
        """
        获取用户对话历史（优化版）
        结合多种数据源获取更全面的历史记录
        """
        try:
            # 1. 首先尝试从对话记录插件获取最近的对话
            recent_history = []
            conversation_plugin = None
            for plugin in getattr(self.handler, 'plugins', []):
                if plugin.__class__.__name__ == 'ConversationHistoryPlugin':
                    conversation_plugin = plugin
                    break

            if conversation_plugin and hasattr(conversation_plugin.db, 'get_conversation_context'):
                recent_history = conversation_plugin.db.get_conversation_context(
                    chat_name=user_name,
                    chat_type='private',
                    limit=limit,
                    session_hours=72  # 获取72小时内的对话
                )
                self.handler._log(f"📝 从对话记录插件获取到 {len(recent_history)} 条最近对话")

            # 2. 获取扩展历史记录（更长时间范围）
            extended_history = []
            try:
                extended_history = self.db.get_user_extended_history(
                    user_name=user_name,
                    days=30,  # 获取30天内的历史
                    limit=50
                )
                self.handler._log(f"📚 获取到 {len(extended_history)} 条扩展历史记录")
            except Exception as e:
                self.handler._log(f"⚠️ 获取扩展历史记录失败: {e}", level="WARNING")

            # 3. 合并和去重历史记录
            all_history = []
            seen_messages = set()

            # 优先使用最近的对话记录
            for msg in recent_history:
                msg_key = f"{msg.get('sender', '')}_{msg.get('message_content', '')}_{msg.get('created_at', '')}"
                if msg_key not in seen_messages:
                    all_history.append(msg)
                    seen_messages.add(msg_key)

            # 补充扩展历史记录
            for msg in extended_history:
                msg_key = f"{msg.get('sender', '')}_{msg.get('message_content', '')}_{msg.get('created_at', '')}"
                if msg_key not in seen_messages and len(all_history) < limit:
                    all_history.append(msg)
                    seen_messages.add(msg_key)

            # 按时间排序
            all_history.sort(key=lambda x: x.get('created_at', ''), reverse=False)

            self.handler._log(f"✅ 总共获取到 {len(all_history)} 条历史对话记录")
            return all_history

        except Exception as e:
            self.handler._log(f"❌ 获取用户对话历史失败: {e}", level="ERROR")
            return []

    def _generate_context_summary(self, user_name: str, conversation_history: List[Dict],
                                 trigger_message: str) -> str:
        """
        AI生成上下文总结（优化版）
        结合历史记录、用户行为模式和当前问题进行综合分析
        """
        try:
            # 1. 获取用户互动模式分析
            interaction_patterns = {}
            historical_issues = []
            try:
                interaction_patterns = self.db.get_user_interaction_patterns(user_name, days=30)
                historical_issues = self.db.get_user_historical_issues(user_name, days=90)
                self.handler._log(f"📊 获取用户行为模式: {len(historical_issues)} 个历史问题")
            except Exception as e:
                self.handler._log(f"⚠️ 获取用户行为模式失败: {e}", level="WARNING")

            # 2. 如果有DeepSeek客户端，使用AI生成增强总结
            if self.deepseek_client:
                enhanced_summary = self._generate_enhanced_ai_summary(
                    user_name, conversation_history, trigger_message,
                    interaction_patterns, historical_issues
                )
                if enhanced_summary:
                    return enhanced_summary

            # 3. 如果没有DeepSeek客户端或API调用失败，使用增强规则分析
            enhanced_summary = self._enhanced_context_analysis(
                user_name, conversation_history, trigger_message,
                interaction_patterns, historical_issues
            )
            return enhanced_summary

        except Exception as e:
            self.handler._log(f"❌ 生成上下文总结失败: {e}", level="ERROR")
            return f"用户 {user_name} 触发转人工关键词: {trigger_message}"

    def _simple_context_analysis(self, context_text: str, trigger_message: str) -> str:
        """简单的上下文分析（可替换为AI接口）"""
        # 分析关键词
        keywords_analysis = {
            '投诉': '用户表达不满，需要投诉处理',
            '退款': '用户要求退款，需要财务处理',
            '问题': '用户遇到技术或服务问题',
            '故障': '系统或产品故障报告',
            '建议': '用户提出改进建议',
            '咨询': '用户咨询相关信息',
            '帮助': '用户需要帮助和支持'
        }

        # 情感分析关键词
        negative_words = ['不满', '生气', '失望', '糟糕', '差', '烂', '垃圾', '坑']
        urgent_words = ['紧急', '急', '马上', '立即', '尽快']

        analysis = []

        # 分析触发原因
        for keyword, description in keywords_analysis.items():
            if keyword in trigger_message or keyword in context_text:
                analysis.append(description)

        # 分析情感倾向
        negative_count = sum(1 for word in negative_words if word in context_text)
        if negative_count > 0:
            analysis.append(f"检测到负面情绪词汇 {negative_count} 个，用户可能情绪不佳")

        # 分析紧急程度
        urgent_count = sum(1 for word in urgent_words if word in context_text)
        if urgent_count > 0:
            analysis.append(f"检测到紧急词汇 {urgent_count} 个，事件可能较为紧急")

        # 分析对话长度
        msg_count = context_text.count('\n') - 3  # 减去标题行
        if msg_count > 10:
            analysis.append(f"对话较长({msg_count}条消息)，用户可能遇到复杂问题")
        elif msg_count < 3:
            analysis.append("对话较短，可能是初次咨询")

        if not analysis:
            analysis.append("用户主动请求转人工服务")

        return "；".join(analysis) + "。"

    def _generate_enhanced_ai_summary(self, user_name: str, conversation_history: List[Dict],
                                    trigger_message: str, interaction_patterns: Dict,
                                    historical_issues: List[Dict]) -> str:
        """使用AI生成增强的上下文总结"""
        try:
            # 构建增强的提示词，包含用户行为模式和历史问题
            enhanced_prompt = self._build_enhanced_prompt(
                user_name, conversation_history, trigger_message,
                interaction_patterns, historical_issues
            )

            # 调用DeepSeek API生成总结
            summary = self.deepseek_client.generate_enhanced_context_summary(enhanced_prompt)
            return summary

        except Exception as e:
            self.handler._log(f"❌ AI增强总结生成失败: {e}", level="ERROR")
            return None

    def _enhanced_context_analysis(self, user_name: str, conversation_history: List[Dict],
                                 trigger_message: str, interaction_patterns: Dict,
                                 historical_issues: List[Dict]) -> str:
        """增强的上下文分析（规则版本）"""
        analysis_parts = []

        # 1. 基础问题分析
        basic_analysis = self._simple_context_analysis(
            self._build_context_text(user_name, conversation_history, trigger_message),
            trigger_message
        )
        analysis_parts.append(f"当前问题: {basic_analysis}")

        # 2. 用户行为模式分析
        if interaction_patterns:
            behavior_analysis = self._analyze_user_behavior(interaction_patterns)
            if behavior_analysis:
                analysis_parts.append(f"用户特征: {behavior_analysis}")

        # 3. 历史问题模式分析
        if historical_issues:
            history_analysis = self._analyze_historical_issues(historical_issues)
            if history_analysis:
                analysis_parts.append(f"历史模式: {history_analysis}")

        # 4. 综合建议
        recommendations = self._generate_handling_recommendations(
            trigger_message, interaction_patterns, historical_issues
        )
        if recommendations:
            analysis_parts.append(f"处理建议: {recommendations}")

        return "；".join(analysis_parts) + "。"

    def _build_context_text(self, user_name: str, conversation_history: List[Dict], trigger_message: str) -> str:
        """构建上下文文本"""
        context_text = f"用户: {user_name}\n"
        context_text += f"触发消息: {trigger_message}\n\n"
        context_text += "最近对话历史:\n"

        for msg in conversation_history[-10:]:  # 只取最近10条
            sender = msg.get('sender', '用户')
            content = msg.get('message_content', '')
            timestamp = msg.get('created_at', '')
            context_text += f"[{timestamp}] {sender}: {content}\n"

        return context_text

    def _analyze_user_behavior(self, patterns: Dict) -> str:
        """分析用户行为模式"""
        behavior_traits = []

        # 活跃度分析
        total_messages = patterns.get('total_messages', 0)
        active_days = patterns.get('active_days', 0)
        avg_messages = patterns.get('avg_messages_per_day', 0)

        if total_messages > 100:
            behavior_traits.append("高频用户")
        elif total_messages > 20:
            behavior_traits.append("活跃用户")
        else:
            behavior_traits.append("新用户")

        # 互动频率分析
        if avg_messages > 10:
            behavior_traits.append("互动频繁")
        elif avg_messages > 3:
            behavior_traits.append("正常互动")
        else:
            behavior_traits.append("偶尔互动")

        # 活跃时间分析
        most_active_hour = patterns.get('most_active_hour')
        if most_active_hour is not None:
            if 9 <= most_active_hour <= 17:
                behavior_traits.append("工作时间活跃")
            elif 18 <= most_active_hour <= 22:
                behavior_traits.append("晚间活跃")
            else:
                behavior_traits.append("非常规时间活跃")

        return "，".join(behavior_traits) if behavior_traits else ""

    def _analyze_historical_issues(self, historical_issues: List[Dict]) -> str:
        """分析历史问题模式"""
        if not historical_issues:
            return ""

        issue_patterns = []

        # 问题频率分析
        issue_count = len(historical_issues)
        if issue_count > 5:
            issue_patterns.append("频繁转人工用户")
        elif issue_count > 2:
            issue_patterns.append("偶有问题用户")
        else:
            issue_patterns.append("首次或少量转人工")

        # 问题类型分析
        keywords = [issue.get('trigger_keyword', '') for issue in historical_issues]
        keyword_counts = {}
        for keyword in keywords:
            keyword_counts[keyword] = keyword_counts.get(keyword, 0) + 1

        if keyword_counts:
            most_common = max(keyword_counts.items(), key=lambda x: x[1])
            if most_common[1] > 1:
                issue_patterns.append(f"常见问题类型: {most_common[0]}")

        # 处理状态分析
        statuses = [issue.get('status', '') for issue in historical_issues]
        pending_count = statuses.count('pending')
        if pending_count > 0:
            issue_patterns.append(f"有{pending_count}个待处理问题")

        return "，".join(issue_patterns) if issue_patterns else ""

    def _generate_handling_recommendations(self, trigger_message: str,
                                         interaction_patterns: Dict,
                                         historical_issues: List[Dict]) -> str:
        """生成处理建议"""
        recommendations = []

        # 基于历史问题的建议
        if historical_issues:
            if len(historical_issues) > 3:
                recommendations.append("重点用户，需优先处理")

            # 检查是否有未解决的问题
            pending_issues = [issue for issue in historical_issues if issue.get('status') == 'pending']
            if pending_issues:
                recommendations.append("存在未解决问题，需要跟进")

        # 基于用户活跃度的建议
        if interaction_patterns.get('total_messages', 0) > 50:
            recommendations.append("活跃用户，建议快速响应")

        # 基于触发关键词的建议
        urgent_keywords = ['投诉', '退款', '故障', '紧急']
        if any(keyword in trigger_message for keyword in urgent_keywords):
            recommendations.append("问题紧急，需立即处理")

        return "，".join(recommendations) if recommendations else ""

    def _build_enhanced_prompt(self, user_name: str, conversation_history: List[Dict],
                             trigger_message: str, interaction_patterns: Dict,
                             historical_issues: List[Dict]) -> str:
        """构建增强的AI提示词"""
        prompt_parts = []

        # 1. 基本信息
        prompt_parts.append(f"用户名: {user_name}")
        prompt_parts.append(f"当前触发消息: {trigger_message}")
        prompt_parts.append(f"触发时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # 2. 最近对话历史
        prompt_parts.append("\n=== 最近对话历史 ===")
        for msg in conversation_history[-15:]:  # 取最近15条
            sender = msg.get('sender', '用户')
            content = msg.get('message_content', '')
            timestamp = msg.get('created_at', '')
            prompt_parts.append(f"[{timestamp}] {sender}: {content}")

        # 3. 用户行为模式
        if interaction_patterns:
            prompt_parts.append("\n=== 用户行为模式 ===")
            prompt_parts.append(f"总消息数: {interaction_patterns.get('total_messages', 0)}")
            prompt_parts.append(f"活跃天数: {interaction_patterns.get('active_days', 0)}")
            prompt_parts.append(f"日均消息: {interaction_patterns.get('avg_messages_per_day', 0):.1f}")

            most_active_hour = interaction_patterns.get('most_active_hour')
            if most_active_hour is not None:
                prompt_parts.append(f"最活跃时间: {most_active_hour}:00")

            message_types = interaction_patterns.get('message_types', {})
            if message_types:
                types_str = ", ".join([f"{k}:{v}" for k, v in message_types.items()])
                prompt_parts.append(f"消息类型分布: {types_str}")

        # 4. 历史问题记录
        if historical_issues:
            prompt_parts.append("\n=== 历史问题记录 ===")
            prompt_parts.append(f"历史转人工次数: {len(historical_issues)}")

            # 最近3个问题的详情
            for i, issue in enumerate(historical_issues[:3]):
                prompt_parts.append(f"问题{i+1}: {issue.get('trigger_keyword', '')} - {issue.get('trigger_message', '')}")
                prompt_parts.append(f"  时间: {issue.get('handover_time', '')}")
                prompt_parts.append(f"  状态: {issue.get('status', '')}")
                if issue.get('context_summary'):
                    prompt_parts.append(f"  总结: {issue.get('context_summary', '')}")

        # 5. 当前时间上下文
        current_time = datetime.now()
        prompt_parts.append(f"\n=== 当前时间信息 ===")
        prompt_parts.append(f"当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S %A')}")

        # 判断是否工作时间
        try:
            is_work_time, time_type = self._is_work_time()
            time_desc = {
                'work_hours': '工作时间',
                'non_work_hours': '非工作时间',
                'weekend': '周末',
                'holiday': '节假日'
            }.get(time_type, '未知时间')
            prompt_parts.append(f"时间类型: {time_desc}")
        except:
            prompt_parts.append("时间类型: 无法判断")

        prompt_parts.append("\n=== 分析要求 ===")
        prompt_parts.append("请基于以上信息，生成专业的用户问题分析总结，包括用户特征、问题紧急程度、处理建议等。")

        return "\n".join(prompt_parts)

    def _generate_user_tags(self, user_name: str, conversation_history: List[Dict],
                           trigger_message: str, context_summary: str) -> List[Dict]:
        """生成用户标签"""
        tags = []

        try:
            # 如果有DeepSeek客户端，使用AI生成标签
            if self.deepseek_client:
                ai_tags = self.deepseek_client.generate_user_tags(
                    user_name, conversation_history, trigger_message, context_summary
                )
                if ai_tags:
                    tags.extend(ai_tags)

            # 如果没有AI标签或AI标签不足，补充基础标签
            if len(tags) < 3:
                fallback_tags = self._generate_fallback_tags(trigger_message, conversation_history, context_summary)
                # 避免重复标签
                existing_tag_names = {tag['tag_name'] for tag in tags}
                for tag in fallback_tags:
                    if tag['tag_name'] not in existing_tag_names:
                        tags.append(tag)

            # 保存标签到数据库
            for tag in tags:
                self.db.add_user_tag(
                    user_name=user_name,
                    tag_name=tag['tag_name'],
                    tag_value=tag['tag_value'],
                    tag_type='auto',
                    confidence=tag['confidence']
                )

            self.handler._log(f"📊 为用户 '{user_name}' 生成了 {len(tags)} 个标签")
            return tags

        except Exception as e:
            self.handler._log(f"❌ 生成用户标签失败: {e}", level="ERROR")
            return []

    def _generate_fallback_tags(self, trigger_message: str, conversation_history: List[Dict],
                               context_summary: str) -> List[Dict]:
        """生成备用标签（当AI生成失败时使用）"""
        tags = []

        # 基于触发关键词的标签
        keyword_tags = {
            '投诉': ('问题类型', '投诉'),
            '退款': ('问题类型', '退款'),
            '故障': ('问题类型', '技术故障'),
            '咨询': ('问题类型', '咨询'),
            '建议': ('问题类型', '建议反馈')
        }

        for keyword, (tag_name, tag_value) in keyword_tags.items():
            if keyword in trigger_message:
                tags.append({
                    'tag_name': tag_name,
                    'tag_value': tag_value,
                    'confidence': 0.9
                })

        # 基于对话历史的标签
        if conversation_history:
            msg_count = len(conversation_history)
            if msg_count > 20:
                tags.append({
                    'tag_name': '用户活跃度',
                    'tag_value': '高频用户',
                    'confidence': 0.8
                })
            elif msg_count > 5:
                tags.append({
                    'tag_name': '用户活跃度',
                    'tag_value': '普通用户',
                    'confidence': 0.7
                })
            else:
                tags.append({
                    'tag_name': '用户活跃度',
                    'tag_value': '新用户',
                    'confidence': 0.6
                })

        # 基于时间的标签（使用工作时间判断）
        is_work_time, time_type = self._is_work_time()
        time_tag_mapping = {
            'work_hours': '工作时间',
            'non_work_hours': '非工作时间',
            'weekend': '周末时间',
            'holiday': '节假日时间'
        }

        tags.append({
            'tag_name': '咨询时间',
            'tag_value': time_tag_mapping.get(time_type, '未知时间'),
            'confidence': 1.0
        })

        # 情感标签
        negative_words = ['不满', '生气', '失望', '糟糕', '差', '烂', '垃圾']
        context_lower = (trigger_message + context_summary).lower()
        negative_count = sum(1 for word in negative_words if word in context_lower)

        if negative_count > 2:
            tags.append({
                'tag_name': '情感倾向',
                'tag_value': '负面情绪',
                'confidence': 0.8
            })
        elif negative_count > 0:
            tags.append({
                'tag_name': '情感倾向',
                'tag_value': '轻微不满',
                'confidence': 0.6
            })
        else:
            tags.append({
                'tag_name': '情感倾向',
                'tag_value': '中性',
                'confidence': 0.5
            })

        return tags

    def _export_handover_report(self, record_id: int, user_name: str, trigger_keyword: str,
                               trigger_message: str, context_summary: str, user_tags: List[Dict],
                               conversation_history: List[Dict]):
        """导出转人工报告到文本文件"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"handover_{user_name}_{timestamp}.txt"
            filepath = os.path.join(self.output_dir, filename)

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write("=" * 60 + "\n")
                f.write("私聊转人工报告\n")
                f.write("=" * 60 + "\n\n")

                # 基本信息
                f.write("【基本信息】\n")
                f.write(f"记录ID: {record_id}\n")
                f.write(f"用户名称: {user_name}\n")
                f.write(f"触发关键词: {trigger_keyword}\n")
                f.write(f"触发时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"触发消息: {trigger_message}\n\n")

                # AI总结
                f.write("【AI上下文总结】\n")
                f.write(f"{context_summary}\n\n")

                # 用户标签
                f.write("【用户标签】\n")
                if user_tags:
                    for tag in user_tags:
                        confidence_str = f"({tag['confidence']:.1%})" if tag.get('confidence') else ""
                        f.write(f"- {tag['tag_name']}: {tag['tag_value']} {confidence_str}\n")
                else:
                    f.write("暂无标签\n")
                f.write("\n")

                # 对话历史
                f.write("【对话历史】\n")
                if conversation_history:
                    for msg in conversation_history:
                        sender = msg.get('sender', '用户')
                        content = msg.get('message_content', '')
                        timestamp = msg.get('created_at', '')
                        f.write(f"[{timestamp}] {sender}: {content}\n")
                else:
                    f.write("暂无对话历史\n")
                f.write("\n")

                # 处理建议
                f.write("【处理建议】\n")
                suggestions = self._generate_handling_suggestions(trigger_keyword, context_summary, user_tags)
                for suggestion in suggestions:
                    f.write(f"- {suggestion}\n")
                f.write("\n")

                # 状态信息
                f.write("【状态信息】\n")
                f.write("处理状态: 待处理\n")
                f.write("自动加入黑名单: 是\n")
                f.write("生成时间: " + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + "\n")

                f.write("\n" + "=" * 60 + "\n")

            self.handler._log(f"📄 转人工报告已导出: {filepath}")

        except Exception as e:
            self.handler._log(f"❌ 导出转人工报告失败: {e}", level="ERROR")

    def _generate_handling_suggestions(self, trigger_keyword: str, context_summary: str,
                                     user_tags: List[Dict]) -> List[str]:
        """生成处理建议"""
        suggestions = []

        # 基于触发关键词的建议
        keyword_suggestions = {
            '投诉': [
                '优先级：高',
                '建议1小时内回复',
                '需要了解具体投诉内容',
                '可能需要上级介入处理'
            ],
            '退款': [
                '转财务部门处理',
                '核实退款原因和金额',
                '按退款流程操作',
                '预计1-3个工作日处理完成'
            ],
            '故障': [
                '转技术支持团队',
                '收集详细故障信息',
                '提供临时解决方案',
                '跟进修复进度'
            ],
            '咨询': [
                '提供详细解答',
                '如需要可提供相关文档',
                '确认用户是否满意答复'
            ]
        }

        for keyword, keyword_suggestions_list in keyword_suggestions.items():
            if keyword in trigger_keyword:
                suggestions.extend(keyword_suggestions_list)
                break

        # 基于用户标签的建议
        for tag in user_tags:
            if tag['tag_name'] == '情感倾向' and tag['tag_value'] in ['负面情绪', '轻微不满']:
                suggestions.append('用户情绪不佳，需要耐心安抚')
            elif tag['tag_name'] == '用户活跃度' and tag['tag_value'] == '高频用户':
                suggestions.append('重要用户，需要优先处理')
            elif tag['tag_name'] == '咨询时间' and tag['tag_value'] == '非工作时间':
                suggestions.append('非工作时间咨询，可能较为紧急')

        # 基于上下文的建议
        if '紧急' in context_summary or '急' in context_summary:
            suggestions.append('事件紧急，需要立即处理')

        if '复杂问题' in context_summary:
            suggestions.append('问题复杂，可能需要多部门协作')

        if not suggestions:
            suggestions.append('按标准流程处理')
            suggestions.append('及时回复用户')

        return suggestions

    def _add_to_blacklist(self, user_name: str):
        """将用户添加到黑名单"""
        try:
            # 查找黑名单插件
            blacklist_plugin = None
            for plugin in getattr(self.handler, 'plugins', []):
                if plugin.__class__.__name__ == 'BlacklistPlugin':
                    blacklist_plugin = plugin
                    break

            if blacklist_plugin:
                success = blacklist_plugin.add_to_blacklist(
                    chat_name=user_name,
                    chat_type='private',
                    is_global=False
                )
                if success:
                    self.handler._log(f"🚫 用户 '{user_name}' 已自动加入黑名单")
                else:
                    self.handler._log(f"⚠️ 用户 '{user_name}' 加入黑名单失败", level="WARNING")
            else:
                self.handler._log("⚠️ 未找到黑名单插件，无法自动加入黑名单", level="WARNING")

        except Exception as e:
            self.handler._log(f"❌ 添加用户到黑名单失败: {e}", level="ERROR")

    # ==================== 管理接口 ==================== #

    def add_trigger_keyword(self, keyword: str, priority: int = 0) -> bool:
        """添加触发关键词"""
        try:
            success = self.db.add_handover_keyword(keyword, priority)
            if success:
                self.trigger_keywords = self._load_trigger_keywords()  # 重新加载
                self.handler._log(f"✅ 添加触发关键词成功: {keyword}")
            return success
        except Exception as e:
            self.handler._log(f"❌ 添加触发关键词失败: {e}", level="ERROR")
            return False

    def remove_trigger_keyword(self, keyword: str) -> bool:
        """删除触发关键词"""
        try:
            success = self.db.remove_handover_keyword(keyword)
            if success:
                self.trigger_keywords = self._load_trigger_keywords()  # 重新加载
                self.handler._log(f"✅ 删除触发关键词成功: {keyword}")
            return success
        except Exception as e:
            self.handler._log(f"❌ 删除触发关键词失败: {e}", level="ERROR")
            return False

    def get_user_profile(self, user_name: str) -> Dict:
        """获取用户档案"""
        try:
            profile = {
                'user_name': user_name,
                'tags': self.db.get_user_tags(user_name),
                'statistics': self.db.get_user_statistics(user_name),
                'handover_records': self.db.get_handover_records(user_name=user_name, limit=10)
            }
            return profile
        except Exception as e:
            self.handler._log(f"❌ 获取用户档案失败: {e}", level="ERROR")
            return {}

    def search_users_by_tag(self, tag_name: str, tag_value: str = None) -> List[Dict]:
        """根据标签搜索用户"""
        try:
            return self.db.search_users_by_tag(tag_name, tag_value)
        except Exception as e:
            self.handler._log(f"❌ 搜索用户失败: {e}", level="ERROR")
            return []

    def get_handover_statistics(self) -> Dict:
        """获取转人工统计信息"""
        try:
            # 获取今日转人工数量
            today_records = self.db.get_handover_records(limit=1000)
            today = datetime.now().date()
            today_count = sum(1 for record in today_records
                            if record['handover_time'].date() == today)

            # 获取待处理数量
            pending_count = len(self.db.get_handover_records(status='pending', limit=1000))

            # 获取标签统计
            tag_summary = self.db.get_all_tags_summary()

            return {
                'today_handover_count': today_count,
                'pending_count': pending_count,
                'total_records': len(today_records),
                'tag_summary': tag_summary
            }
        except Exception as e:
            self.handler._log(f"❌ 获取转人工统计失败: {e}", level="ERROR")
            return {}

    def update_handover_status(self, record_id: int, status: str, notes: str = None) -> bool:
        """更新转人工记录状态"""
        try:
            return self.db.update_handover_record(record_id, status, notes)
        except Exception as e:
            self.handler._log(f"❌ 更新转人工状态失败: {e}", level="ERROR")
            return False
