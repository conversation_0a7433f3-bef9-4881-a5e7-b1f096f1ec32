#!/usr/bin/env python3
"""
定时消息表结构迁移脚本
确保数据库表结构与最新版本兼容
"""

import sys
import traceback
from utils.config_utils import load_config
from db_plugins.mysql import MySQLDB


def migrate_scheduled_message_tables():
    """迁移定时消息表结构"""
    print("🔄 开始迁移定时消息表结构...")
    
    try:
        # 连接数据库
        config = load_config()
        db_config = config.get("mysql", {})
        db = MySQLDB(**db_config)
        db.connect()
        
        cursor = db.connection.cursor()
        
        # 1. 检查并更新 scheduled_messages 表结构
        print("🔍 检查 scheduled_messages 表结构...")
        
        # 检查是否存在 message_type 列
        cursor.execute("""
            SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'scheduled_messages' 
            AND COLUMN_NAME = 'message_type'
        """)
        
        if cursor.fetchone()[0] == 0:
            print("➕ 添加 message_type 列...")
            cursor.execute("""
                ALTER TABLE scheduled_messages 
                ADD COLUMN message_type ENUM('text', 'file', 'emotion', 'url_card', 'mixed') 
                NOT NULL DEFAULT 'text' COMMENT '消息类型' 
                AFTER chat_type
            """)
            
            # 为现有记录设置默认值
            cursor.execute("""
                UPDATE scheduled_messages 
                SET message_type = 'text' 
                WHERE message_type IS NULL OR message_type = ''
            """)
            print("✅ message_type 列添加成功")
        else:
            print("✅ message_type 列已存在")
        
        # 检查并添加索引
        print("🔍 检查索引...")
        cursor.execute("""
            SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'scheduled_messages' 
            AND INDEX_NAME = 'idx_message_type'
        """)
        
        if cursor.fetchone()[0] == 0:
            print("➕ 添加 message_type 索引...")
            cursor.execute("""
                ALTER TABLE scheduled_messages 
                ADD INDEX idx_message_type (message_type)
            """)
            print("✅ message_type 索引添加成功")
        else:
            print("✅ message_type 索引已存在")
        
        # 2. 创建 scheduled_message_contents 表（如果不存在）
        print("🔍 检查 scheduled_message_contents 表...")
        cursor.execute("""
            SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'scheduled_message_contents'
        """)
        
        if cursor.fetchone()[0] == 0:
            print("🏗️ 创建 scheduled_message_contents 表...")
            cursor.execute("""
                CREATE TABLE scheduled_message_contents (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    message_id INT NOT NULL COMMENT '关联的消息任务ID',
                    content_type ENUM('text', 'file', 'emotion', 'url_card') NOT NULL COMMENT '内容类型',
                    content_order INT NOT NULL DEFAULT 0 COMMENT '发送顺序',
                    text_content TEXT NULL COMMENT '文本内容',
                    file_paths JSON NULL COMMENT '文件路径列表',
                    emotion_index INT NULL COMMENT '表情索引',
                    url_content VARCHAR(500) NULL COMMENT 'URL地址',
                    send_delay FLOAT DEFAULT 0 COMMENT '发送延迟(秒)',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_message_id (message_id),
                    INDEX idx_content_order (message_id, content_order),
                    FOREIGN KEY (message_id) REFERENCES scheduled_messages(id) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """)
            print("✅ scheduled_message_contents 表创建成功")
        else:
            print("✅ scheduled_message_contents 表已存在")
        
        # 3. 更新 scheduled_message_history 表结构
        print("🔍 检查 scheduled_message_history 表结构...")
        
        # 检查是否存在 message_type 列
        cursor.execute("""
            SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'scheduled_message_history' 
            AND COLUMN_NAME = 'message_type'
        """)
        
        if cursor.fetchone()[0] == 0:
            print("➕ 更新 scheduled_message_history 表结构...")
            cursor.execute("""
                ALTER TABLE scheduled_message_history 
                ADD COLUMN message_type ENUM('text', 'file', 'emotion', 'url_card', 'mixed') 
                NOT NULL DEFAULT 'text' COMMENT '消息类型' 
                AFTER chat_type
            """)
            
            # 重命名旧的 message_content 列为 message_summary
            cursor.execute("""
                SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'scheduled_message_history' 
                AND COLUMN_NAME = 'message_content'
            """)
            
            if cursor.fetchone()[0] > 0:
                cursor.execute("""
                    ALTER TABLE scheduled_message_history 
                    CHANGE COLUMN message_content message_summary TEXT NULL COMMENT '消息摘要'
                """)
                print("✅ message_content 列重命名为 message_summary")
            
            # 添加新列
            cursor.execute("""
                ALTER TABLE scheduled_message_history 
                ADD COLUMN sent_count INT DEFAULT 0 COMMENT '成功发送的内容数量' AFTER error_message,
                ADD COLUMN total_count INT DEFAULT 0 COMMENT '总内容数量' AFTER sent_count
            """)
            
            print("✅ scheduled_message_history 表结构更新成功")
        else:
            print("✅ scheduled_message_history 表结构已是最新")
        
        # 4. 迁移现有数据
        print("🔄 迁移现有数据...")
        
        # 查找所有文本类型的任务，为它们创建对应的内容记录
        cursor.execute("""
            SELECT id, message_content FROM scheduled_messages 
            WHERE message_type = 'text' AND message_content IS NOT NULL 
            AND id NOT IN (SELECT DISTINCT message_id FROM scheduled_message_contents)
        """)
        
        text_tasks = cursor.fetchall()
        migrated_count = 0
        
        for task_id, message_content in text_tasks:
            try:
                cursor.execute("""
                    INSERT INTO scheduled_message_contents 
                    (message_id, content_type, content_order, text_content, send_delay)
                    VALUES (%s, 'text', 0, %s, 0)
                """, (task_id, message_content))
                migrated_count += 1
            except Exception as e:
                print(f"⚠️ 迁移任务 {task_id} 失败: {e}")
        
        if migrated_count > 0:
            print(f"✅ 成功迁移 {migrated_count} 个文本任务的内容")
        else:
            print("✅ 无需迁移现有数据")
        
        # 5. 提交所有更改
        db.connection.commit()
        print("✅ 所有更改已提交")
        
        # 6. 验证表结构
        print("🔍 验证表结构...")
        
        # 验证主表
        cursor.execute("""
            SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'scheduled_messages' 
            ORDER BY ORDINAL_POSITION
        """)
        main_columns = [row[0] for row in cursor.fetchall()]
        print(f"📋 scheduled_messages 表列: {', '.join(main_columns)}")
        
        # 验证内容表
        cursor.execute("""
            SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'scheduled_message_contents' 
            ORDER BY ORDINAL_POSITION
        """)
        content_columns = [row[0] for row in cursor.fetchall()]
        print(f"📋 scheduled_message_contents 表列: {', '.join(content_columns)}")
        
        # 验证历史表
        cursor.execute("""
            SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'scheduled_message_history' 
            ORDER BY ORDINAL_POSITION
        """)
        history_columns = [row[0] for row in cursor.fetchall()]
        print(f"📋 scheduled_message_history 表列: {', '.join(history_columns)}")
        
        print("\n🎉 表结构迁移完成！")
        return True
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        traceback.print_exc()
        try:
            db.connection.rollback()
            print("🔄 已回滚更改")
        except:
            pass
        return False


def test_migrated_structure():
    """测试迁移后的表结构"""
    print("\n🧪 测试迁移后的表结构...")
    
    try:
        # 连接数据库
        config = load_config()
        db_config = config.get("mysql", {})
        db = MySQLDB(**db_config)
        db.connect()
        
        # 注册SQL函数
        from plugins.scheduled_message.sql import SCHEDULED_MESSAGE_SQL_FUNCTIONS
        db.register_plugin_functions("MigrationTest", SCHEDULED_MESSAGE_SQL_FUNCTIONS)
        
        # 测试添加一个简单的文本消息
        from datetime import time
        success = db.add_scheduled_message(
            task_name="迁移测试任务",
            chat_name="测试群",
            chat_type="group",
            message_type="text",
            message_content="这是一条迁移测试消息",
            schedule_type="daily",
            schedule_time=time(9, 0)
        )
        
        if success:
            print("✅ 添加测试任务成功")
            
            # 获取任务
            task = db.get_scheduled_message_by_name("迁移测试任务")
            if task:
                print(f"✅ 获取任务成功: {task['task_name']} ({task['message_type']})")
                
                # 清理测试数据
                db.delete_scheduled_message("迁移测试任务")
                print("✅ 清理测试数据成功")
            else:
                print("❌ 获取任务失败")
        else:
            print("❌ 添加测试任务失败")
        
        # 卸载SQL函数
        db.unregister_plugin_functions("MigrationTest")
        
        print("✅ 表结构测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 表结构测试失败: {e}")
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("=" * 60)
    print("🔧 定时消息表结构迁移工具")
    print("=" * 60)
    
    # 执行迁移
    if migrate_scheduled_message_tables():
        # 测试迁移结果
        if test_migrated_structure():
            print("\n🎉 迁移和测试都成功完成！")
            print("\n💡 现在可以安全地使用增强版定时消息功能了")
            return True
        else:
            print("\n⚠️ 迁移完成但测试失败，请检查表结构")
            return False
    else:
        print("\n❌ 迁移失败，请检查错误信息")
        return False


if __name__ == "__main__":
    main()
