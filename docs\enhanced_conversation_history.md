# 增强的对话历史记录存储逻辑

## 概述

本次优化针对对话历史记录存储逻辑进行了全面改进，实现了用户消息和AI回复的精确关联存储，能够智能区分群聊和私聊场景，支持不同的消息处理策略。

## 主要优化内容

### 1. 增强数据库表结构

#### 新增字段

在 `conversation_history` 表中新增了以下字段：

```sql
conversation_id VARCHAR(64) NULL COMMENT '对话ID，用于关联用户消息和AI回复'
reply_to_id BIGINT NULL COMMENT '回复的消息ID，用于建立回复关系'
message_batch_id VARCHAR(64) NULL COMMENT '消息批次ID，用于标识同一批次的消息'
is_batch_start BOOLEAN DEFAULT FALSE COMMENT '是否为批次开始'
is_batch_end BOOLEAN DEFAULT FALSE COMMENT '是否为批次结束'
```

#### 索引优化

```sql
INDEX idx_conversation_id (conversation_id)
INDEX idx_reply_to_id (reply_to_id)
INDEX idx_message_batch_id (message_batch_id)
```

### 2. 智能消息存储策略

#### 群聊场景

**用户连续多条消息**：
- 使用 `message_batch_id` 将连续消息关联为一个批次
- 第一条消息标记 `is_batch_start = TRUE`
- 最后一条消息标记 `is_batch_end = TRUE`
- AI回复关联到批次中的最后一条消息

**用户单条消息**：
- 直接存储，不使用批次ID
- AI回复直接关联到该消息

#### 私聊场景

**用户多个问题**：
- 每条消息单独存储
- 使用相同的 `conversation_id` 关联同一次交互
- AI可以选择合并回复或分别回复每个问题

**用户单个问题**：
- 单条存储，包含 `conversation_id`
- AI单条回复，直接关联

### 3. 新增SQL函数

#### `save_message_batch`
```python
def save_message_batch(self, chat_name: str, chat_type: str, sender: str,
                      messages: List[str], message_type: str = 'user', 
                      session_hours: int = 24, conversation_id: str = None) -> Tuple[str, List[int]]
```
批量保存消息并返回批次ID和消息ID列表。

#### `save_ai_reply_with_context`
```python
def save_ai_reply_with_context(self, chat_name: str, chat_type: str, 
                              ai_response: str, user_message_ids: List[int],
                              session_hours: int = 24, conversation_id: str = None) -> int
```
保存AI回复并建立与用户消息的关联。

#### `get_conversation_with_replies`
```python
def get_conversation_with_replies(self, chat_name: str, chat_type: str, 
                                 limit: int = 50, session_hours: int = 24) -> List[Dict]
```
获取包含回复关系的对话记录。

#### `get_user_message_batches`
```python
def get_user_message_batches(self, chat_name: str, chat_type: str, sender: str,
                           limit: int = 10, session_hours: int = 24) -> List[Dict]
```
获取用户的消息批次信息。

### 4. AI回复拆分机制

#### 多问题回复处理

当用户在私聊中发送多个问题时，系统可以：

1. **按段落拆分**：如果AI回复包含多个段落，按段落分配给不同问题
2. **按句号拆分**：将长回复按句号分组，分配给不同问题
3. **保持完整**：如果无法有效拆分，保持完整回复

#### 拆分算法

```python
def _split_ai_response_for_multiple_questions(self, ai_response: str, question_count: int) -> List[str]:
    # 1. 尝试按段落拆分
    paragraphs = [p.strip() for p in ai_response.split('\n\n') if p.strip()]
    if len(paragraphs) >= question_count:
        return paragraphs[:question_count]
    
    # 2. 尝试按句号拆分并分组
    sentences = [s.strip() + '。' for s in ai_response.split('。') if s.strip()]
    if len(sentences) >= question_count:
        # 将句子分组分配
        return grouped_sentences
    
    # 3. 无法拆分时返回完整回复
    return [ai_response] * question_count
```

## 使用场景示例

### 场景1：群聊用户连续消息

**用户输入**：
```
张三: 大家好
张三: 我想问个问题  
张三: 这个产品怎么使用？
```

**存储结果**：
```sql
-- 用户消息（批次存储）
INSERT INTO conversation_history VALUES 
(1, 'group_chat', 'group', '张三', '大家好', 'user', 'session_123', 'conv_456', NULL, 'batch_789', TRUE, FALSE, NOW()),
(2, 'group_chat', 'group', '张三', '我想问个问题', 'user', 'session_123', 'conv_456', NULL, 'batch_789', FALSE, FALSE, NOW()),
(3, 'group_chat', 'group', '张三', '这个产品怎么使用？', 'user', 'session_123', 'conv_456', NULL, 'batch_789', FALSE, TRUE, NOW());

-- AI回复（关联到最后一条用户消息）
INSERT INTO conversation_history VALUES 
(4, 'group_chat', 'group', 'AI助手', '您好！关于产品使用...', 'bot', 'session_123', 'conv_456', 3, NULL, FALSE, FALSE, NOW());
```

### 场景2：私聊用户多个问题

**用户输入**：
```
王五: 产品价格是多少？
王五: 有什么优惠活动吗？
王五: 支持哪些支付方式？
```

**存储结果**：
```sql
-- 用户消息（单独存储）
INSERT INTO conversation_history VALUES 
(5, '王五', 'private', '王五', '产品价格是多少？', 'user', 'session_124', 'conv_457', NULL, NULL, FALSE, FALSE, NOW()),
(6, '王五', 'private', '王五', '有什么优惠活动吗？', 'user', 'session_124', 'conv_457', NULL, NULL, FALSE, FALSE, NOW()),
(7, '王五', 'private', '王五', '支持哪些支付方式？', 'user', 'session_124', 'conv_457', NULL, NULL, FALSE, FALSE, NOW());

-- AI回复（可能拆分为多条）
INSERT INTO conversation_history VALUES 
(8, '王五', 'private', 'AI助手', '关于价格，我们的基础版是99元/月。', 'bot', 'session_124', 'conv_457', 5, NULL, FALSE, FALSE, NOW()),
(9, '王五', 'private', 'AI助手', '关于优惠，目前有新用户8折优惠。', 'bot', 'session_124', 'conv_457', 6, NULL, FALSE, FALSE, NOW()),
(10, '王五', 'private', 'AI助手', '关于支付，支持微信、支付宝、银行卡。', 'bot', 'session_124', 'conv_457', 7, NULL, FALSE, FALSE, NOW());
```

## 数据查询示例

### 获取完整对话关系

```sql
SELECT 
    ch.*,
    reply_to.sender as reply_to_sender,
    reply_to.message_content as reply_to_content
FROM conversation_history ch
LEFT JOIN conversation_history reply_to ON ch.reply_to_id = reply_to.id
WHERE ch.chat_name = '王五' AND ch.chat_type = 'private'
ORDER BY ch.created_at ASC;
```

### 获取用户消息批次

```sql
SELECT 
    message_batch_id,
    COUNT(*) as message_count,
    MIN(created_at) as batch_start_time,
    MAX(created_at) as batch_end_time,
    GROUP_CONCAT(message_content ORDER BY created_at SEPARATOR ' ') as combined_content
FROM conversation_history
WHERE chat_name = 'group_chat' AND sender = '张三' 
AND message_batch_id IS NOT NULL
GROUP BY message_batch_id
ORDER BY batch_start_time DESC;
```

## 配置参数

### 推荐配置

```python
config = {
    'session_hours': 24,              # 会话持续时间
    'enable_batch_storage': True,     # 启用批次存储
    'enable_reply_splitting': True,   # 启用回复拆分
    'max_questions_per_reply': 5,     # 单次回复最大问题数
    'split_strategy': 'auto',         # 拆分策略：auto/paragraph/sentence
}
```

## 性能优化

1. **索引优化**：为新增字段添加合适的索引
2. **批量操作**：使用事务处理批量消息存储
3. **查询优化**：使用JOIN查询减少数据库访问次数
4. **缓存机制**：对频繁查询的对话记录进行缓存

## 监控指标

- 消息存储成功率
- 回复关联准确率
- 批次处理效率
- 查询响应时间
- 存储空间使用情况

## 后续扩展

1. **智能回复分配**：基于问题相似度的回复分配
2. **对话主题识别**：自动识别对话主题并分类
3. **用户行为分析**：基于消息模式的用户画像
4. **实时对话状态**：支持对话状态的实时更新
