import os
import json
import uuid
from typing import List, Optional, Dict, Any
from datetime import datetime
# 抽象基类 Plugin（假设你已有定义）
from core.plugin_base import Plugin
from db_plugins.mysql import MySQLDB  # 假设你有一个 MySQLDB 类来处理数据库操作
# 导入插件的SQL函数
from .sql import KEYWORD_REPLY_SQL_FUNCTIONS
class KeywordReplyPlugin(Plugin):
    """
    关键词回复插件：从 MySQL 数据库中读取规则。
    支持：
    - 区分群聊和私聊，默认规则不同
    - 每个聊天窗口单独启用/禁用
    默认优先级：10
    """

    priority = 10

    def __init__(self, handler):
        super().__init__(handler)

        # 加载数据库配置
        # config_path = os.path.join(
        #     os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'config.json'
        # )
        # config_path = r"E:\wechat_test_all\wechat_bot\config.json"  # 直接写绝对路径
        # 使用主处理程序提供的数据库连接
        self.db = getattr(handler, 'db', None)
        if not self.db:
            raise ValueError("❌ handler 未绑定数据库实例，无法继续")

        # 注册本插件所需的 SQL 函数
        self.db.register_plugin_functions("KeywordReplyPlugin", KEYWORD_REPLY_SQL_FUNCTIONS)


        # 缓存规则数据
        self.cache = self._load_all_rules_from_db()

    def __del__(self):
        """插件析构时卸载SQL函数"""
        if hasattr(self, 'db') and self.db:
            self.db.unregister_plugin_functions("KeywordReplyPlugin")

    def _load_all_rules_from_db(self) -> Dict[str, Any]:
        """从数据库加载所有关键词规则"""
        results = self.db.get_all_keyword_rules()

        cache = {
            "default_group_rule": {},
            "default_private_rule": {},
            "chat_specific_rules": {}
        }

        chat_data = {}

        # 先按 chat_name 分组整理数据
        for row in results:
            chat_name = row["chat_name"]
            chat_type = row["chat_type"]
            enabled = row["enabled"]
            use_global_rules = row["use_global_rules"]
            global_rule = row["global_rule"]
            keyword = row["user_keyword"]
            reply = row["default_reply"]

            if chat_name not in chat_data:
                chat_data[chat_name] = {
                    "type": chat_type,
                    "enabled": enabled,
                    "use_global_rules": use_global_rules,
                    "rules": {}
                }
            if keyword and reply:
                chat_data[chat_name]["rules"][keyword] = reply

        # 构建缓存
        for chat_name, data in chat_data.items():
            if data.get("global_rule", False):
                if data["type"] == "group":
                    cache["default_group_rule"].update(data["rules"])
                else:
                    cache["default_private_rule"].update(data["rules"])
            else:
                cache["chat_specific_rules"][chat_name] = {
                    "type": data["type"],
                    "enabled": data["enabled"],
                    "use_global_rules": data["use_global_rules"],
                    "rules": data["rules"]
                }

        return cache

    def on_messages(self, chat: str, messages: List[str]) -> Optional[str]:
        """
        处理消息并返回关键词回复（增强版）
        支持增强的对话历史记录存储
        """
        chat_config = self.cache["chat_specific_rules"].get(chat, {})
        chat_enabled = chat_config.get("enabled", True)
        use_global_rules = chat_config.get("use_global_rules", False)
        chat_type = chat_config.get("type", "group")  # 默认为群聊
        chat_rules = chat_config.get("rules", {})

        # 如果局部禁用但允许使用全局规则，则清空局部规则
        if not chat_enabled:
            if use_global_rules:
                chat_rules = {}
            else:
                return None

        # 尝试匹配局部规则
        matched_result = self._match_rules_enhanced(chat_rules, messages, chat, chat_type, is_global=False)
        if matched_result:
            return matched_result

        # 尝试匹配全局规则
        if use_global_rules:
            if chat_type == "group":
                global_rules = self.cache["default_group_rule"]
            else:
                global_rules = self.cache["default_private_rule"]
            matched_result = self._match_rules_enhanced(global_rules, messages, chat, chat_type, is_global=True)
            if matched_result:
                return matched_result

        return None

    def _match_rules(self, rules: dict, messages: List[str], chat: str, is_global: bool) -> Optional[str]:
        """原始的规则匹配方法（保持兼容性）"""
        for msg in reversed(messages):
            for keyword, reply in rules.items():
                if keyword in msg:
                    source = "全局" if is_global else "局部"
                    rule_type = "群聊" if "group" in (self.cache["chat_specific_rules"].get(chat, {}).get("type", "group")) else "私聊"
                    self.handler._log(f"[关键词回复] 【{chat}】【{rule_type}】{source}匹配 '{keyword}'，回复：{reply}")
                    return reply
        return None

    def _match_rules_enhanced(self, rules: dict, messages: List[str], chat: str,
                            chat_type: str, is_global: bool) -> Optional[str]:
        """
        增强的规则匹配方法
        支持对话历史记录存储和消息关联
        """
        for msg in reversed(messages):
            for keyword, reply in rules.items():
                if keyword in msg:
                    source = "全局" if is_global else "局部"
                    self.handler._log(f"[关键词回复] 【{chat}】【{chat_type}】{source}匹配 '{keyword}'，回复：{reply}")

                    # 存储用户消息和AI回复到对话历史
                    self._save_conversation_with_reply(chat, chat_type, messages, reply, keyword)

                    return reply
        return None

    def _save_conversation_with_reply(self, chat: str, chat_type: str,
                                    user_messages: List[str], ai_reply: str,
                                    matched_keyword: str):
        """
        保存用户消息和AI回复到对话历史记录
        使用增强的存储逻辑
        """
        try:
            # 获取对话历史插件
            conversation_plugin = self._get_conversation_history_plugin()
            if not conversation_plugin:
                self.handler._log("[关键词回复] 未找到对话历史插件，跳过历史记录存储", level="WARNING")
                return

            # 获取真实的发送者信息
            real_sender = self._get_real_sender(chat, chat_type, user_messages, conversation_plugin)

            # 生成对话ID用于关联这次交互
            conversation_id = str(uuid.uuid4())

            # 根据聊天类型决定存储策略
            user_message_ids = []

            if chat_type == 'group':
                # 群聊：如果有多条消息，作为批次存储
                if len(user_messages) > 1:
                    _, message_ids = conversation_plugin.db.save_message_batch(
                        chat_name=chat,
                        chat_type=chat_type,
                        sender=real_sender,
                        messages=user_messages,
                        message_type='user',
                        conversation_id=conversation_id
                    )
                    user_message_ids = message_ids
                else:
                    # 单条消息
                    message_id = conversation_plugin.db.save_message(
                        chat_name=chat,
                        chat_type=chat_type,
                        sender=real_sender,
                        message_content=user_messages[0],
                        message_type='user',
                        conversation_id=conversation_id
                    )
                    user_message_ids = [message_id]
            else:
                # 私聊：每条消息单独存储
                for msg in user_messages:
                    message_id = conversation_plugin.db.save_message(
                        chat_name=chat,
                        chat_type=chat_type,
                        sender=real_sender,
                        message_content=msg,
                        message_type='user',
                        conversation_id=conversation_id
                    )
                    user_message_ids.append(message_id)

            # 保存AI回复并建立关联
            ai_reply_with_context = f"[关键词回复] 匹配关键词: {matched_keyword}\n{ai_reply}"
            ai_message_id = conversation_plugin.db.save_ai_reply_with_context(
                chat_name=chat,
                chat_type=chat_type,
                ai_response=ai_reply_with_context,
                user_message_ids=user_message_ids,
                conversation_id=conversation_id
            )

            self.handler._log(f"[关键词回复] 已保存对话记录，发送者: {real_sender}，关联消息数: {len(user_message_ids)}，AI回复ID: {ai_message_id}")

        except Exception as e:
            self.handler._log(f"[关键词回复] 保存对话历史失败: {e}", level="ERROR")

    def _get_real_sender(self, chat: str, chat_type: str, user_messages: List[str],
                        conversation_plugin) -> str:
        """
        获取真实的发送者信息
        通过查询最近的对话历史来推断发送者
        """
        try:
            # 私聊情况下，发送者就是聊天窗口名称
            if chat_type == 'private':
                return chat

            # 群聊情况下，尝试从最近的对话历史中获取发送者
            # 获取最近的几条用户消息
            recent_context = conversation_plugin.db.get_conversation_context(
                chat_name=chat,
                chat_type=chat_type,
                limit=10,
                session_hours=1  # 只查看最近1小时的消息
            )

            # 查找最近发送过相同内容的用户
            for msg_content in reversed(user_messages):  # 从最新的消息开始查找
                for context_msg in reversed(recent_context):  # 从最新的历史开始查找
                    if (context_msg.get('message_type') == 'user' and
                        context_msg.get('message_content') == msg_content):
                        sender = context_msg.get('sender')
                        if sender and sender != 'AI助手':
                            self.handler._log(f"[关键词回复] 从历史记录中找到发送者: {sender}")
                            return sender

            # 如果找不到匹配的历史记录，返回默认值
            self.handler._log("[关键词回复] 无法确定发送者，使用默认值", level="WARNING")
            return '未知用户'

        except Exception as e:
            self.handler._log(f"[关键词回复] 获取发送者信息失败: {e}", level="ERROR")
            # 私聊返回聊天名称，群聊返回未知用户
            return chat if chat_type == 'private' else '未知用户'

    def _get_conversation_history_plugin(self):
        """获取对话历史插件实例"""
        try:
            # 从handler的插件列表中查找对话历史插件
            for plugin in getattr(self.handler, 'plugins', []):
                if plugin.__class__.__name__ == 'ConversationHistoryPlugin':
                    return plugin
            return None
        except Exception as e:
            self.handler._log(f"[关键词回复] 获取对话历史插件失败: {e}", level="ERROR")
            return None