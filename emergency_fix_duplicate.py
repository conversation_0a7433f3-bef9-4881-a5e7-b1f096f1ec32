#!/usr/bin/env python3
"""
紧急修复重复发送问题
立即禁用所有重复发送的一次性任务
"""

import sys
import traceback
from utils.config_utils import load_config
from db_plugins.mysql import MySQLDB


def emergency_fix():
    """紧急修复重复发送问题"""
    print("🚨 紧急修复重复发送问题")
    print("=" * 60)
    
    try:
        # 连接数据库
        config = load_config()
        db_config = config.get("mysql", {})
        db = MySQLDB(**db_config)
        db.connect()
        
        cursor = db.connection.cursor(dictionary=True)
        
        # 1. 立即禁用所有已发送过的一次性任务
        print("🛑 立即禁用所有已发送过的一次性任务...")
        cursor.execute("""
            UPDATE scheduled_messages 
            SET enabled = FALSE, next_send_at = NULL 
            WHERE schedule_type = 'once' 
            AND send_count > 0 
            AND enabled = TRUE
        """)
        
        disabled_count = cursor.rowcount
        print(f"✅ 禁用了 {disabled_count} 个重复发送的一次性任务")
        
        # 2. 查看当前状态
        print("\n📋 当前任务状态:")
        cursor.execute("""
            SELECT task_name, schedule_type, enabled, send_count, next_send_at
            FROM scheduled_messages 
            ORDER BY task_name
        """)
        
        tasks = cursor.fetchall()
        for task in tasks:
            status = "🟢 启用" if task['enabled'] else "🔴 禁用"
            next_send = task['next_send_at'] or "无"
            print(f"  - {task['task_name']} ({task['schedule_type']}) {status} 发送{task['send_count']}次 下次:{next_send}")
        
        # 3. 检查待发送任务
        print("\n📋 待发送任务:")
        cursor.execute("""
            SELECT task_name, chat_name, schedule_type, next_send_at
            FROM scheduled_messages 
            WHERE enabled = TRUE 
            AND (next_send_at IS NULL OR next_send_at <= NOW())
            ORDER BY next_send_at
        """)
        
        pending = cursor.fetchall()
        if pending:
            print(f"⚠️ 仍有 {len(pending)} 个待发送任务:")
            for task in pending:
                print(f"  - {task['task_name']} -> {task['chat_name']} ({task['schedule_type']})")
        else:
            print("✅ 没有待发送任务")
        
        # 4. 提交更改
        db.connection.commit()
        print(f"\n✅ 紧急修复完成！禁用了 {disabled_count} 个任务")
        
        return True
        
    except Exception as e:
        print(f"❌ 紧急修复失败: {e}")
        traceback.print_exc()
        return False


def restart_plugin_suggestion():
    """建议重启插件"""
    print("\n💡 建议操作:")
    print("1. 🔄 重启微信机器人程序以应用修复")
    print("2. 📋 使用 simple_message_manager.py 查看任务状态")
    print("3. 🧹 清理不需要的任务")
    print("4. ⚠️ 观察是否还有重复发送")
    
    print("\n🚀 修复说明:")
    print("- ✅ 一次性任务发送成功后会自动禁用")
    print("- ✅ next_send_at 会设置为 NULL")
    print("- ✅ enabled 会设置为 FALSE")
    print("- ✅ 不会再出现在待发送列表中")


def main():
    """主函数"""
    print("🚨 定时消息重复发送紧急修复")
    print("=" * 60)
    
    if emergency_fix():
        restart_plugin_suggestion()
        print("\n🎉 紧急修复成功！")
        print("⚠️ 请重启微信机器人程序以确保修复生效")
        return True
    else:
        print("\n❌ 紧急修复失败")
        return False


if __name__ == "__main__":
    main()
