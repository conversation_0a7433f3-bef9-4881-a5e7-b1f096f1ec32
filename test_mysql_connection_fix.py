#!/usr/bin/env python3
"""
测试MySQL连接修复
验证数据库连接状态检查和自动重连机制是否正常工作
"""

import os
import sys
import time
from utils.config_utils import load_config
from db_plugins.mysql import MySQLDB


def test_mysql_connection_fix():
    """测试MySQL连接修复功能"""
    print("🔍 测试MySQL连接修复功能...")
    
    try:
        # 加载配置
        config = load_config()
        db_config = config.get("mysql", {})
        if not db_config:
            print("❌ 未找到数据库配置")
            return False
        
        # 创建数据库连接
        db = MySQLDB(**db_config)
        print("📋 创建数据库实例成功")
        
        # 测试初始连接
        print("🔌 测试初始连接...")
        db.connect()
        print("✅ 初始连接成功")
        
        # 测试连接状态检查
        print("🔍 测试连接状态检查...")
        if db.is_connected():
            print("✅ 连接状态检查正常")
        else:
            print("❌ 连接状态检查失败")
            return False
        
        # 注册用户信息插件的SQL函数
        print("📝 注册用户信息插件SQL函数...")
        from plugins.user_info.sql import USER_INFO_SQL_FUNCTIONS
        db.register_plugin_functions("TestUserInfo", USER_INFO_SQL_FUNCTIONS)
        print("✅ SQL函数注册成功")
        
        # 测试数据库表初始化
        print("🏗️ 测试数据库表初始化...")
        db.init_user_data_tables()
        print("✅ 数据库表初始化成功")
        
        # 测试记录程序状态
        print("📊 测试记录程序状态...")
        db.record_program_status('startup')
        print("✅ 程序状态记录成功")
        
        # 测试获取配置
        print("⚙️ 测试获取用户数据配置...")
        config_data = db.get_user_data_config()
        if config_data:
            print(f"✅ 配置获取成功: {config_data}")
        else:
            print("⚠️ 未找到配置数据，但这是正常的")
        
        # 测试更新配置
        print("🔄 测试更新配置...")
        db.update_user_data_config(
            force_collection_on_startup=True,
            collection_interval_hours=12
        )
        print("✅ 配置更新成功")
        
        # 再次获取配置验证更新
        print("🔍 验证配置更新...")
        updated_config = db.get_user_data_config()
        if updated_config:
            print(f"✅ 配置验证成功: {updated_config}")
            if updated_config['force_collection_on_startup'] and updated_config['collection_interval_hours'] == 12:
                print("✅ 配置更新验证通过")
            else:
                print("❌ 配置更新验证失败")
                return False
        
        # 测试获取最后程序状态
        print("📖 测试获取最后程序状态...")
        last_status = db.get_last_program_status()
        if last_status:
            status, timestamp = last_status
            print(f"✅ 最后状态获取成功: {status} at {timestamp}")
        else:
            print("⚠️ 未找到状态记录")
        
        # 测试更新最后采集时间
        print("⏰ 测试更新最后采集时间...")
        db.update_last_collection_time()
        print("✅ 最后采集时间更新成功")
        
        # 测试获取最后采集时间
        print("📅 测试获取最后采集时间...")
        last_collection = db.get_last_data_collection_time()
        if last_collection:
            print(f"✅ 最后采集时间获取成功: {last_collection}")
        else:
            print("⚠️ 未找到采集时间记录")
        
        # 清理
        db.unregister_plugin_functions("TestUserInfo")
        print("🧹 SQL函数卸载成功")
        
        print("\n🎉 所有测试通过！MySQL连接修复功能正常工作")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_connection_recovery():
    """测试连接恢复功能"""
    print("\n🔄 测试连接恢复功能...")
    
    try:
        # 加载配置
        config = load_config()
        db_config = config.get("mysql", {})
        
        # 创建数据库连接
        db = MySQLDB(**db_config)
        db.connect()
        
        # 注册SQL函数
        from plugins.user_info.sql import USER_INFO_SQL_FUNCTIONS
        db.register_plugin_functions("TestRecovery", USER_INFO_SQL_FUNCTIONS)
        
        print("✅ 初始连接建立成功")
        
        # 模拟连接断开（关闭连接）
        print("🔌 模拟连接断开...")
        if db.connection:
            db.connection.close()
        
        # 检查连接状态
        if not db.is_connected():
            print("✅ 连接断开检测正常")
        else:
            print("❌ 连接断开检测失败")
            return False
        
        # 尝试执行数据库操作（应该自动重连）
        print("🔄 尝试执行数据库操作（测试自动重连）...")
        db.record_program_status('test_recovery')
        print("✅ 自动重连成功，数据库操作正常")
        
        # 验证连接已恢复
        if db.is_connected():
            print("✅ 连接恢复验证成功")
        else:
            print("❌ 连接恢复验证失败")
            return False
        
        # 清理
        db.unregister_plugin_functions("TestRecovery")
        
        print("🎉 连接恢复测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 连接恢复测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("=" * 60)
    print("MySQL连接修复功能测试")
    print("=" * 60)
    
    # 测试基本功能
    test1_result = test_mysql_connection_fix()
    
    # 测试连接恢复
    test2_result = test_connection_recovery()
    
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print(f"基本功能测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"连接恢复测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！MySQL连接问题已修复")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
        sys.exit(1)
