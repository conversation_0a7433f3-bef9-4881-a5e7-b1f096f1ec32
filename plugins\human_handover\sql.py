"""
Human Handover Plugin SQL Functions
包含私聊转人工插件所需的所有SQL查询函数
"""
from typing import Optional, List, Dict
from datetime import datetime, timedelta


def init_human_handover_tables(self):
    """初始化转人工相关表"""
    cursor = self.connection.cursor()

    # 创建转人工触发关键词表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS handover_keywords (
            id INT AUTO_INCREMENT PRIMARY KEY,
            keyword VARCHAR(100) NOT NULL COMMENT '触发关键词',
            enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
            priority INT DEFAULT 0 COMMENT '优先级，数字越小越优先',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_keyword (keyword),
            INDEX idx_enabled_priority (enabled, priority)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """)

    # 创建用户标签表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS user_tags (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_name VARCHAR(100) NOT NULL COMMENT '用户名称',
            tag_name VARCHAR(50) NOT NULL COMMENT '标签名称',
            tag_value TEXT NULL COMMENT '标签值',
            tag_type ENUM('auto', 'manual') DEFAULT 'auto' COMMENT '标签类型：自动生成或手动添加',
            confidence FLOAT DEFAULT 1.0 COMMENT '置信度(0-1)',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_user_tag (user_name, tag_name),
            INDEX idx_user_name (user_name),
            INDEX idx_tag_name (tag_name),
            INDEX idx_tag_type (tag_type)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """)

    # 创建转人工记录表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS handover_records (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_name VARCHAR(100) NOT NULL COMMENT '用户名称',
            trigger_keyword VARCHAR(100) NOT NULL COMMENT '触发关键词',
            trigger_message TEXT NOT NULL COMMENT '触发消息',
            context_summary TEXT NULL COMMENT 'AI生成的上下文总结',
            user_tags JSON NULL COMMENT '用户标签快照',
            conversation_history JSON NULL COMMENT '对话历史快照',
            handover_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '转人工时间',
            status ENUM('pending', 'handled', 'closed') DEFAULT 'pending' COMMENT '处理状态',
            handler_notes TEXT NULL COMMENT '人工处理备注',
            auto_blacklisted BOOLEAN DEFAULT FALSE COMMENT '是否自动加入黑名单',
            INDEX idx_user_name (user_name),
            INDEX idx_handover_time (handover_time),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """)

    # 创建工作时间配置表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS work_schedule_config (
            id INT AUTO_INCREMENT PRIMARY KEY,
            config_key VARCHAR(50) NOT NULL COMMENT '配置键',
            config_value TEXT NOT NULL COMMENT '配置值',
            config_type ENUM('boolean', 'string', 'json', 'integer') DEFAULT 'string' COMMENT '配置类型',
            description TEXT NULL COMMENT '配置描述',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_config_key (config_key)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """)

    # 插入默认触发关键词
    cursor.execute("""
        INSERT IGNORE INTO handover_keywords (keyword, priority) VALUES
        ('转人工', 1),
        ('人工客服', 2),
        ('联系客服', 3),
        ('投诉', 4),
        ('退款', 5),
        ('问题反馈', 6),
        ('人工服务', 7),
        ('客服', 8),
        ('转接', 9),
        ('帮助', 10)
    """)

    # 插入默认工作时间配置
    cursor.execute("""
        INSERT IGNORE INTO work_schedule_config (config_key, config_value, config_type, description) VALUES
        ('enabled', 'true', 'boolean', '是否启用工作时间功能'),
        ('work_days', '[1,2,3,4,5]', 'json', '工作日设置（1=周一，7=周日）'),
        ('work_start', '09:00', 'string', '工作开始时间'),
        ('work_end', '18:00', 'string', '工作结束时间'),
        ('timezone', 'Asia/Shanghai', 'string', '时区设置'),
        ('holiday_mode', 'false', 'boolean', '节假日模式'),
        ('work_hours_response', '您好！我已为您转接人工客服，客服人员会尽快为您处理。', 'string', '工作时间响应消息'),
        ('non_work_hours_response', '您好！现在是非工作时间（工作时间：周一至周五 09:00-18:00），您的问题已记录，客服人员会在工作时间内优先为您处理。如有紧急问题，请留下详细描述。', 'string', '非工作时间响应消息'),
        ('weekend_response', '您好！现在是周末时间，您的问题已记录，客服人员会在下个工作日优先为您处理。如有紧急问题，请留下详细描述。', 'string', '周末时间响应消息'),
        ('holiday_response', '您好！现在是节假日期间，您的问题已记录，客服人员会在工作日恢复后优先为您处理。感谢您的理解！', 'string', '节假日响应消息')
    """)

    try:
        self.connection.commit()
    except Exception as e:
        self.connection.rollback()
        raise e


def get_handover_keywords(self) -> List[Dict]:
    """获取所有启用的转人工关键词"""
    cursor = self.connection.cursor(dictionary=True)
    cursor.execute("""
        SELECT keyword, priority FROM handover_keywords
        WHERE enabled = TRUE
        ORDER BY priority ASC, keyword ASC
    """)
    return cursor.fetchall()


def add_handover_keyword(self, keyword: str, priority: int = 0) -> bool:
    """添加转人工关键词"""
    cursor = self.connection.cursor()
    cursor.execute("""
        INSERT INTO handover_keywords (keyword, priority)
        VALUES (%s, %s)
        ON DUPLICATE KEY UPDATE
        enabled = TRUE, priority = VALUES(priority), updated_at = NOW()
    """, (keyword, priority))
    self.connection.commit()
    return cursor.rowcount > 0


def remove_handover_keyword(self, keyword: str) -> bool:
    """删除转人工关键词"""
    cursor = self.connection.cursor()
    cursor.execute("""
        DELETE FROM handover_keywords WHERE keyword = %s
    """, (keyword,))
    self.connection.commit()
    return cursor.rowcount > 0


def get_user_tags(self, user_name: str) -> List[Dict]:
    """获取用户的所有标签"""
    cursor = self.connection.cursor(dictionary=True)
    cursor.execute("""
        SELECT tag_name, tag_value, tag_type, confidence, created_at, updated_at
        FROM user_tags
        WHERE user_name = %s
        ORDER BY created_at DESC
    """, (user_name,))
    return cursor.fetchall()


def add_user_tag(self, user_name: str, tag_name: str, tag_value: str = None,
                tag_type: str = 'auto', confidence: float = 1.0) -> bool:
    """添加或更新用户标签"""
    cursor = self.connection.cursor()
    cursor.execute("""
        INSERT INTO user_tags (user_name, tag_name, tag_value, tag_type, confidence)
        VALUES (%s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
        tag_value = VALUES(tag_value),
        tag_type = VALUES(tag_type),
        confidence = VALUES(confidence),
        updated_at = NOW()
    """, (user_name, tag_name, tag_value, tag_type, confidence))
    self.connection.commit()
    return cursor.rowcount > 0


def remove_user_tag(self, user_name: str, tag_name: str) -> bool:
    """删除用户标签"""
    cursor = self.connection.cursor()
    cursor.execute("""
        DELETE FROM user_tags
        WHERE user_name = %s AND tag_name = %s
    """, (user_name, tag_name))
    self.connection.commit()
    return cursor.rowcount > 0


def create_handover_record(self, user_name: str, trigger_keyword: str, trigger_message: str,
                          context_summary: str = None, user_tags: List[Dict] = None,
                          conversation_history: List[Dict] = None, auto_blacklisted: bool = False) -> int:
    """创建转人工记录"""
    import json
    from datetime import datetime

    def json_serializer(obj):
        """自定义JSON序列化器，处理datetime对象"""
        if isinstance(obj, datetime):
            return obj.strftime('%Y-%m-%d %H:%M:%S')
        raise TypeError(f"Object of type {type(obj)} is not JSON serializable")

    # 预处理数据，确保所有datetime对象都被转换
    def preprocess_data(data):
        """预处理数据，将datetime对象转换为字符串"""
        if isinstance(data, list):
            return [preprocess_data(item) for item in data]
        elif isinstance(data, dict):
            return {key: preprocess_data(value) for key, value in data.items()}
        elif isinstance(data, datetime):
            return data.strftime('%Y-%m-%d %H:%M:%S')
        else:
            return data

    # 预处理conversation_history和user_tags
    processed_user_tags = preprocess_data(user_tags) if user_tags else None
    processed_conversation_history = preprocess_data(conversation_history) if conversation_history else None

    cursor = self.connection.cursor()
    cursor.execute("""
        INSERT INTO handover_records
        (user_name, trigger_keyword, trigger_message, context_summary,
         user_tags, conversation_history, auto_blacklisted)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
    """, (
        user_name, trigger_keyword, trigger_message, context_summary,
        json.dumps(processed_user_tags, ensure_ascii=False) if processed_user_tags else None,
        json.dumps(processed_conversation_history, ensure_ascii=False) if processed_conversation_history else None,
        auto_blacklisted
    ))
    self.connection.commit()
    return cursor.lastrowid


def get_handover_records(self, user_name: str = None, status: str = None,
                        limit: int = 100) -> List[Dict]:
    """获取转人工记录"""
    cursor = self.connection.cursor(dictionary=True)

    where_conditions = []
    params = []

    if user_name:
        where_conditions.append("user_name = %s")
        params.append(user_name)

    if status:
        where_conditions.append("status = %s")
        params.append(status)

    where_clause = "WHERE " + " AND ".join(where_conditions) if where_conditions else ""
    params.append(limit)

    cursor.execute(f"""
        SELECT * FROM handover_records
        {where_clause}
        ORDER BY handover_time DESC
        LIMIT %s
    """, params)

    return cursor.fetchall()


def update_handover_record(self, record_id: int, status: str = None,
                          handler_notes: str = None) -> bool:
    """更新转人工记录"""
    cursor = self.connection.cursor()

    updates = []
    params = []

    if status:
        updates.append("status = %s")
        params.append(status)

    if handler_notes:
        updates.append("handler_notes = %s")
        params.append(handler_notes)

    if not updates:
        return False

    params.append(record_id)

    cursor.execute(f"""
        UPDATE handover_records
        SET {', '.join(updates)}
        WHERE id = %s
    """, params)

    self.connection.commit()
    return cursor.rowcount > 0


def get_user_statistics(self, user_name: str) -> Dict:
    """获取用户统计信息"""
    cursor = self.connection.cursor(dictionary=True)

    # 获取转人工次数
    cursor.execute("""
        SELECT COUNT(*) as handover_count,
               MAX(handover_time) as last_handover_time
        FROM handover_records
        WHERE user_name = %s
    """, (user_name,))

    stats = cursor.fetchone() or {}

    # 获取标签数量
    cursor.execute("""
        SELECT COUNT(*) as tag_count
        FROM user_tags
        WHERE user_name = %s
    """, (user_name,))

    tag_stats = cursor.fetchone() or {}
    stats.update(tag_stats)

    return stats


def get_user_extended_history(self, user_name: str, days: int = 30, limit: int = 100) -> List[Dict]:
    """
    获取用户扩展历史记录
    包括更长时间范围内的对话记录和行为分析
    """
    cursor = self.connection.cursor(dictionary=True)

    # 计算时间范围
    from datetime import datetime, timedelta
    start_date = datetime.now() - timedelta(days=days)

    # 尝试从conversation_history表获取历史记录
    try:
        cursor.execute("""
            SELECT sender, message_content, message_type, created_at, chat_name, chat_type
            FROM conversation_history
            WHERE (sender = %s OR chat_name = %s)
            AND created_at >= %s
            ORDER BY created_at DESC
            LIMIT %s
        """, (user_name, user_name, start_date, limit))

        return cursor.fetchall()
    except Exception:
        # 如果conversation_history表不存在或查询失败，返回空列表
        return []


def get_user_interaction_patterns(self, user_name: str, days: int = 30) -> Dict:
    """
    分析用户互动模式
    包括活跃时间段、消息频率、问题类型等
    """
    cursor = self.connection.cursor(dictionary=True)

    from datetime import datetime, timedelta
    start_date = datetime.now() - timedelta(days=days)

    patterns = {
        'total_messages': 0,
        'active_days': 0,
        'avg_messages_per_day': 0,
        'most_active_hour': None,
        'message_types': {},
        'recent_topics': []
    }

    try:
        # 获取消息总数和活跃天数
        cursor.execute("""
            SELECT
                COUNT(*) as total_messages,
                COUNT(DISTINCT DATE(created_at)) as active_days
            FROM conversation_history
            WHERE (sender = %s OR chat_name = %s)
            AND created_at >= %s
        """, (user_name, user_name, start_date))

        result = cursor.fetchone()
        if result:
            patterns['total_messages'] = result.get('total_messages', 0)
            patterns['active_days'] = result.get('active_days', 0)
            if patterns['active_days'] > 0:
                patterns['avg_messages_per_day'] = patterns['total_messages'] / patterns['active_days']

        # 获取最活跃时间段
        cursor.execute("""
            SELECT HOUR(created_at) as hour, COUNT(*) as count
            FROM conversation_history
            WHERE (sender = %s OR chat_name = %s)
            AND created_at >= %s
            GROUP BY HOUR(created_at)
            ORDER BY count DESC
            LIMIT 1
        """, (user_name, user_name, start_date))

        hour_result = cursor.fetchone()
        if hour_result:
            patterns['most_active_hour'] = hour_result.get('hour')

        # 获取消息类型分布
        cursor.execute("""
            SELECT message_type, COUNT(*) as count
            FROM conversation_history
            WHERE (sender = %s OR chat_name = %s)
            AND created_at >= %s
            GROUP BY message_type
        """, (user_name, user_name, start_date))

        type_results = cursor.fetchall()
        for result in type_results:
            patterns['message_types'][result['message_type']] = result['count']

    except Exception:
        # 如果查询失败，返回默认值
        pass

    return patterns


def get_user_historical_issues(self, user_name: str, days: int = 90) -> List[Dict]:
    """
    获取用户历史问题记录
    包括之前的转人工记录和相关标签
    """
    cursor = self.connection.cursor(dictionary=True)

    from datetime import datetime, timedelta
    start_date = datetime.now() - timedelta(days=days)

    cursor.execute("""
        SELECT
            hr.id,
            hr.trigger_keyword,
            hr.trigger_message,
            hr.context_summary,
            hr.handover_time,
            hr.status,
            hr.handler_notes,
            GROUP_CONCAT(
                CONCAT(ut.tag_name, ':', ut.tag_value, '(', ut.confidence, ')')
                SEPARATOR '; '
            ) as tags
        FROM handover_records hr
        LEFT JOIN user_tags ut ON hr.user_name = ut.user_name
            AND ut.created_at BETWEEN hr.handover_time - INTERVAL 1 HOUR
            AND hr.handover_time + INTERVAL 1 HOUR
        WHERE hr.user_name = %s
        AND hr.handover_time >= %s
        GROUP BY hr.id
        ORDER BY hr.handover_time DESC
    """, (user_name, start_date))

    return cursor.fetchall()


def search_users_by_tag(self, tag_name: str, tag_value: str = None) -> List[Dict]:
    """根据标签搜索用户"""
    cursor = self.connection.cursor(dictionary=True)

    if tag_value:
        cursor.execute("""
            SELECT DISTINCT user_name, tag_value, confidence, created_at
            FROM user_tags
            WHERE tag_name = %s AND tag_value LIKE %s
            ORDER BY confidence DESC, created_at DESC
        """, (tag_name, f"%{tag_value}%"))
    else:
        cursor.execute("""
            SELECT DISTINCT user_name, tag_value, confidence, created_at
            FROM user_tags
            WHERE tag_name = %s
            ORDER BY confidence DESC, created_at DESC
        """, (tag_name,))

    return cursor.fetchall()


def get_all_tags_summary(self) -> List[Dict]:
    """获取所有标签的统计摘要"""
    cursor = self.connection.cursor(dictionary=True)
    cursor.execute("""
        SELECT tag_name,
               COUNT(*) as user_count,
               AVG(confidence) as avg_confidence,
               MAX(created_at) as last_used
        FROM user_tags
        GROUP BY tag_name
        ORDER BY user_count DESC, tag_name ASC
    """)
    return cursor.fetchall()


def get_work_schedule_config(self, config_key: str = None) -> Dict:
    """获取工作时间配置"""
    cursor = self.connection.cursor(dictionary=True)

    if config_key:
        cursor.execute("""
            SELECT config_key, config_value, config_type, description
            FROM work_schedule_config
            WHERE config_key = %s
        """, (config_key,))
        result = cursor.fetchone()
        if result:
            return _parse_config_value(result)
        return None
    else:
        cursor.execute("""
            SELECT config_key, config_value, config_type, description
            FROM work_schedule_config
            ORDER BY config_key
        """)
        results = cursor.fetchall()

        config = {}
        for row in results:
            parsed = _parse_config_value(row)
            config[parsed['config_key']] = parsed['parsed_value']

        return config


def set_work_schedule_config(self, config_key: str, config_value: str, config_type: str = 'string', description: str = None) -> bool:
    """设置工作时间配置"""
    cursor = self.connection.cursor()

    try:
        cursor.execute("""
            INSERT INTO work_schedule_config (config_key, config_value, config_type, description)
            VALUES (%s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
            config_value = VALUES(config_value),
            config_type = VALUES(config_type),
            description = VALUES(description),
            updated_at = CURRENT_TIMESTAMP
        """, (config_key, config_value, config_type, description))

        self.connection.commit()
        return cursor.rowcount > 0

    except Exception as e:
        self.connection.rollback()
        raise e


def delete_work_schedule_config(self, config_key: str) -> bool:
    """删除工作时间配置"""
    cursor = self.connection.cursor()
    cursor.execute("""
        DELETE FROM work_schedule_config
        WHERE config_key = %s
    """, (config_key,))
    self.connection.commit()
    return cursor.rowcount > 0


def _parse_config_value(config_row: Dict) -> Dict:
    """解析配置值"""
    import json

    config_key = config_row['config_key']
    config_value = config_row['config_value']
    config_type = config_row['config_type']

    # 根据类型解析值
    if config_type == 'boolean':
        parsed_value = config_value.lower() in ('true', '1', 'yes', 'on')
    elif config_type == 'integer':
        parsed_value = int(config_value)
    elif config_type == 'json':
        parsed_value = json.loads(config_value)
    else:  # string
        parsed_value = config_value

    return {
        'config_key': config_key,
        'config_value': config_value,
        'config_type': config_type,
        'parsed_value': parsed_value,
        'description': config_row.get('description')
    }


# 导出所有SQL函数
HUMAN_HANDOVER_SQL_FUNCTIONS = {
    'init_human_handover_tables': init_human_handover_tables,
    'get_handover_keywords': get_handover_keywords,
    'add_handover_keyword': add_handover_keyword,
    'remove_handover_keyword': remove_handover_keyword,
    'get_user_tags': get_user_tags,
    'add_user_tag': add_user_tag,
    'remove_user_tag': remove_user_tag,
    'create_handover_record': create_handover_record,
    'get_handover_records': get_handover_records,
    'update_handover_record': update_handover_record,
    'get_user_statistics': get_user_statistics,
    'search_users_by_tag': search_users_by_tag,
    'get_all_tags_summary': get_all_tags_summary,
    'get_work_schedule_config': get_work_schedule_config,
    'set_work_schedule_config': set_work_schedule_config,
    'delete_work_schedule_config': delete_work_schedule_config,
    'get_user_extended_history': get_user_extended_history,
    'get_user_interaction_patterns': get_user_interaction_patterns,
    'get_user_historical_issues': get_user_historical_issues,
}
