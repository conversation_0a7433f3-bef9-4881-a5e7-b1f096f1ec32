#!/usr/bin/env python3
"""
增强版定时消息管理器
支持文本、文件、表情、链接卡片等多种消息类型的定时发送
"""

import os
import sys
from datetime import datetime, time
from typing import List, Dict
from utils.config_utils import load_config
from db_plugins.mysql import MySQLDB


class EnhancedScheduledMessageManager:
    """增强版定时消息管理器"""
    
    def __init__(self):
        # 加载配置
        self.config = load_config()
        db_config = self.config.get("mysql", {})
        if not db_config:
            raise ValueError("❌ config.json 中未配置 mysql 数据库信息")
        
        # 连接数据库
        self.db = MySQLDB(**db_config)
        self.db.connect()
        
        # 注册SQL函数
        from plugins.scheduled_message.sql import SCHEDULED_MESSAGE_SQL_FUNCTIONS
        self.db.register_plugin_functions("EnhancedScheduledMessageManager", SCHEDULED_MESSAGE_SQL_FUNCTIONS)
        
        # 初始化表
        try:
            self.db.init_scheduled_message_tables()
            print("✅ 数据库表初始化完成")
        except Exception as e:
            print(f"❌ 初始化数据库表失败: {e}")

    def add_text_message(self, task_name: str, chat_name: str, chat_type: str,
                        text_content: str, schedule_type: str, **kwargs):
        """添加文本定时消息"""
        try:
            return self.db.add_scheduled_message(
                task_name=task_name,
                chat_name=chat_name,
                chat_type=chat_type,
                message_type='text',
                message_content=text_content,
                schedule_type=schedule_type,
                **kwargs
            )
        except Exception as e:
            print(f"❌ 添加文本定时消息失败: {e}")
            return False

    def add_file_message(self, task_name: str, chat_name: str, chat_type: str,
                        file_paths: List[str], schedule_type: str, **kwargs):
        """添加文件定时消息"""
        try:
            contents = [{'type': 'file', 'paths': file_paths, 'delay': 0}]
            return self.db.add_scheduled_message(
                task_name=task_name,
                chat_name=chat_name,
                chat_type=chat_type,
                message_type='file',
                schedule_type=schedule_type,
                contents=contents,
                **kwargs
            )
        except Exception as e:
            print(f"❌ 添加文件定时消息失败: {e}")
            return False

    def add_emotion_message(self, task_name: str, chat_name: str, chat_type: str,
                           emotion_index: int, schedule_type: str, **kwargs):
        """添加表情定时消息"""
        try:
            contents = [{'type': 'emotion', 'index': emotion_index, 'delay': 0}]
            return self.db.add_scheduled_message(
                task_name=task_name,
                chat_name=chat_name,
                chat_type=chat_type,
                message_type='emotion',
                schedule_type=schedule_type,
                contents=contents,
                **kwargs
            )
        except Exception as e:
            print(f"❌ 添加表情定时消息失败: {e}")
            return False

    def add_url_card_message(self, task_name: str, chat_name: str, chat_type: str,
                            url: str, schedule_type: str, **kwargs):
        """添加链接卡片定时消息"""
        try:
            contents = [{'type': 'url_card', 'url': url, 'delay': 0}]
            return self.db.add_scheduled_message(
                task_name=task_name,
                chat_name=chat_name,
                chat_type=chat_type,
                message_type='url_card',
                schedule_type=schedule_type,
                contents=contents,
                **kwargs
            )
        except Exception as e:
            print(f"❌ 添加链接卡片定时消息失败: {e}")
            return False

    def add_mixed_message(self, task_name: str, chat_name: str, chat_type: str,
                         contents: List[Dict], schedule_type: str, **kwargs):
        """添加混合类型定时消息"""
        try:
            return self.db.add_scheduled_message(
                task_name=task_name,
                chat_name=chat_name,
                chat_type=chat_type,
                message_type='mixed',
                schedule_type=schedule_type,
                contents=contents,
                **kwargs
            )
        except Exception as e:
            print(f"❌ 添加混合定时消息失败: {e}")
            return False

    def list_all_tasks(self):
        """列出所有定时消息任务"""
        try:
            tasks = self.db.get_all_scheduled_messages()
            if not tasks:
                print("📋 暂无定时消息任务")
                return
            
            print(f"📋 共有 {len(tasks)} 个定时消息任务:")
            print("-" * 80)
            for task in tasks:
                status = "✅ 启用" if task['enabled'] else "❌ 禁用"
                print(f"任务名称: {task['task_name']}")
                print(f"聊天对象: {task['chat_name']} ({'群聊' if task['chat_type'] == 'group' else '私聊'})")
                print(f"消息类型: {task['message_type']}")
                print(f"调度类型: {task['schedule_type']}")
                print(f"状态: {status}")
                print(f"已发送: {task['send_count']} 次")
                if task['next_send_at']:
                    print(f"下次发送: {task['next_send_at']}")
                print("-" * 80)
        except Exception as e:
            print(f"❌ 获取任务列表失败: {e}")

    def delete_task(self, task_name: str):
        """删除定时消息任务"""
        try:
            if self.db.delete_scheduled_message(task_name):
                print(f"✅ 任务 '{task_name}' 删除成功")
            else:
                print(f"❌ 任务 '{task_name}' 删除失败或不存在")
        except Exception as e:
            print(f"❌ 删除任务失败: {e}")

    def enable_task(self, task_name: str, enabled: bool = True):
        """启用/禁用定时消息任务"""
        try:
            if self.db.update_scheduled_message(task_name, enabled=enabled):
                status = "启用" if enabled else "禁用"
                print(f"✅ 任务 '{task_name}' {status}成功")
            else:
                print(f"❌ 任务 '{task_name}' 状态更新失败或不存在")
        except Exception as e:
            print(f"❌ 更新任务状态失败: {e}")

    def show_task_history(self, task_name: str = None, limit: int = 20):
        """显示任务发送历史"""
        try:
            history = self.db.get_message_history(task_name, limit)
            if not history:
                print("📋 暂无发送历史")
                return
            
            print(f"📋 发送历史 (最近 {len(history)} 条):")
            print("-" * 80)
            for record in history:
                status_icon = "✅" if record['status'] == 'success' else "❌"
                print(f"{status_icon} {record['sent_at']} - {record['task_name']}")
                print(f"   聊天对象: {record['chat_name']} ({'群聊' if record['chat_type'] == 'group' else '私聊'})")
                print(f"   消息类型: {record['message_type']}")
                if record['error_message']:
                    print(f"   错误信息: {record['error_message']}")
                print("-" * 80)
        except Exception as e:
            print(f"❌ 获取发送历史失败: {e}")


def main():
    """主函数"""
    print("=" * 60)
    print("🤖 增强版定时消息管理器")
    print("=" * 60)
    
    try:
        manager = EnhancedScheduledMessageManager()
        
        while True:
            print("\n请选择操作:")
            print("1. 添加文本定时消息")
            print("2. 添加文件定时消息")
            print("3. 添加表情定时消息")
            print("4. 添加链接卡片定时消息")
            print("5. 添加混合定时消息")
            print("6. 查看所有任务")
            print("7. 删除任务")
            print("8. 启用/禁用任务")
            print("9. 查看发送历史")
            print("0. 退出")
            
            choice = input("\n请输入选择 (0-9): ").strip()
            
            if choice == '0':
                break
            elif choice == '1':
                add_text_message_interactive(manager)
            elif choice == '2':
                add_file_message_interactive(manager)
            elif choice == '3':
                add_emotion_message_interactive(manager)
            elif choice == '4':
                add_url_card_message_interactive(manager)
            elif choice == '5':
                add_mixed_message_interactive(manager)
            elif choice == '6':
                manager.list_all_tasks()
            elif choice == '7':
                delete_task_interactive(manager)
            elif choice == '8':
                toggle_task_interactive(manager)
            elif choice == '9':
                show_history_interactive(manager)
            else:
                print("❌ 无效选择，请重新输入")
    
    except Exception as e:
        print(f"❌ 程序异常: {e}")
    finally:
        print("👋 程序退出")


def add_text_message_interactive(manager):
    """交互式添加文本消息"""
    print("\n📝 添加文本定时消息")
    task_name = input("任务名称: ").strip()
    chat_name = input("聊天对象: ").strip()
    chat_type = input("聊天类型 (group/private): ").strip()
    text_content = input("文本内容: ").strip()
    
    print("调度类型:")
    print("1. daily - 每日")
    print("2. weekly - 每周")
    print("3. interval - 间隔")
    print("4. once - 一次性")
    
    schedule_choice = input("选择调度类型 (1-4): ").strip()
    
    kwargs = {}
    if schedule_choice == '1':
        schedule_type = 'daily'
        send_time = input("发送时间 (HH:MM): ").strip()
        hour, minute = map(int, send_time.split(':'))
        kwargs['schedule_time'] = time(hour, minute)
    elif schedule_choice == '2':
        schedule_type = 'weekly'
        weekday = int(input("星期几 (0=周一, 6=周日): ").strip())
        send_time = input("发送时间 (HH:MM): ").strip()
        hour, minute = map(int, send_time.split(':'))
        kwargs['schedule_weekday'] = weekday
        kwargs['schedule_time'] = time(hour, minute)
    elif schedule_choice == '3':
        schedule_type = 'interval'
        interval = int(input("间隔分钟数: ").strip())
        kwargs['interval_minutes'] = interval
    elif schedule_choice == '4':
        schedule_type = 'once'
        send_datetime = input("发送时间 (YYYY-MM-DD HH:MM): ").strip()
        send_dt = datetime.strptime(send_datetime, "%Y-%m-%d %H:%M")
        kwargs['schedule_date'] = send_dt.date()
        kwargs['schedule_time'] = send_dt.time()
    else:
        print("❌ 无效选择")
        return
    
    if manager.add_text_message(task_name, chat_name, chat_type, text_content, schedule_type, **kwargs):
        print("✅ 文本定时消息添加成功")
    else:
        print("❌ 文本定时消息添加失败")


def add_file_message_interactive(manager):
    """交互式添加文件消息"""
    print("\n📁 添加文件定时消息")
    task_name = input("任务名称: ").strip()
    chat_name = input("聊天对象: ").strip()
    chat_type = input("聊天类型 (group/private): ").strip()
    
    file_paths = []
    while True:
        file_path = input("文件路径 (回车结束): ").strip()
        if not file_path:
            break
        file_paths.append(file_path)
    
    if not file_paths:
        print("❌ 未输入文件路径")
        return
    
    # 简化调度设置，使用每日调度
    send_time = input("发送时间 (HH:MM): ").strip()
    hour, minute = map(int, send_time.split(':'))
    
    if manager.add_file_message(task_name, chat_name, chat_type, file_paths, 'daily', schedule_time=time(hour, minute)):
        print("✅ 文件定时消息添加成功")
    else:
        print("❌ 文件定时消息添加失败")


def add_emotion_message_interactive(manager):
    """交互式添加表情消息"""
    print("\n😊 添加表情定时消息")
    task_name = input("任务名称: ").strip()
    chat_name = input("聊天对象: ").strip()
    chat_type = input("聊天类型 (group/private): ").strip()
    emotion_index = int(input("表情索引: ").strip())
    
    send_time = input("发送时间 (HH:MM): ").strip()
    hour, minute = map(int, send_time.split(':'))
    
    if manager.add_emotion_message(task_name, chat_name, chat_type, emotion_index, 'daily', schedule_time=time(hour, minute)):
        print("✅ 表情定时消息添加成功")
    else:
        print("❌ 表情定时消息添加失败")


def add_url_card_message_interactive(manager):
    """交互式添加链接卡片消息"""
    print("\n🔗 添加链接卡片定时消息")
    task_name = input("任务名称: ").strip()
    chat_name = input("聊天对象: ").strip()
    chat_type = input("聊天类型 (group/private): ").strip()
    url = input("链接地址: ").strip()
    
    send_time = input("发送时间 (HH:MM): ").strip()
    hour, minute = map(int, send_time.split(':'))
    
    if manager.add_url_card_message(task_name, chat_name, chat_type, url, 'daily', schedule_time=time(hour, minute)):
        print("✅ 链接卡片定时消息添加成功")
    else:
        print("❌ 链接卡片定时消息添加失败")


def add_mixed_message_interactive(manager):
    """交互式添加混合消息"""
    print("\n🎭 添加混合定时消息")
    task_name = input("任务名称: ").strip()
    chat_name = input("聊天对象: ").strip()
    chat_type = input("聊天类型 (group/private): ").strip()
    
    contents = []
    while True:
        print("\n添加内容项:")
        print("1. 文本")
        print("2. 文件")
        print("3. 表情")
        print("4. 链接卡片")
        print("0. 完成")
        
        content_choice = input("选择内容类型 (0-4): ").strip()
        
        if content_choice == '0':
            break
        elif content_choice == '1':
            text = input("文本内容: ").strip()
            delay = float(input("发送延迟(秒): ").strip() or "0")
            contents.append({'type': 'text', 'content': text, 'delay': delay})
        elif content_choice == '2':
            paths = input("文件路径(用逗号分隔): ").strip().split(',')
            delay = float(input("发送延迟(秒): ").strip() or "0")
            contents.append({'type': 'file', 'paths': [p.strip() for p in paths], 'delay': delay})
        elif content_choice == '3':
            index = int(input("表情索引: ").strip())
            delay = float(input("发送延迟(秒): ").strip() or "0")
            contents.append({'type': 'emotion', 'index': index, 'delay': delay})
        elif content_choice == '4':
            url = input("链接地址: ").strip()
            delay = float(input("发送延迟(秒): ").strip() or "0")
            contents.append({'type': 'url_card', 'url': url, 'delay': delay})
    
    if not contents:
        print("❌ 未添加任何内容")
        return
    
    send_time = input("发送时间 (HH:MM): ").strip()
    hour, minute = map(int, send_time.split(':'))
    
    if manager.add_mixed_message(task_name, chat_name, chat_type, contents, 'daily', schedule_time=time(hour, minute)):
        print("✅ 混合定时消息添加成功")
    else:
        print("❌ 混合定时消息添加失败")


def delete_task_interactive(manager):
    """交互式删除任务"""
    task_name = input("请输入要删除的任务名称: ").strip()
    if task_name:
        manager.delete_task(task_name)


def toggle_task_interactive(manager):
    """交互式启用/禁用任务"""
    task_name = input("请输入任务名称: ").strip()
    if not task_name:
        return
    
    action = input("启用(1)还是禁用(0): ").strip()
    if action == '1':
        manager.enable_task(task_name, True)
    elif action == '0':
        manager.enable_task(task_name, False)
    else:
        print("❌ 无效选择")


def show_history_interactive(manager):
    """交互式显示历史"""
    task_name = input("任务名称(回车显示所有): ").strip()
    limit = input("显示条数(默认20): ").strip()
    limit = int(limit) if limit else 20
    
    manager.show_task_history(task_name if task_name else None, limit)


if __name__ == "__main__":
    main()
