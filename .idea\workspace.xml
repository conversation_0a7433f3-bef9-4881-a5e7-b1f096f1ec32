<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="8e68c24f-f353-49c5-a1f8-e442f92bb48a" name="Changes" comment="优化读取历史记录生成标签逻辑，不是获取新发的几条信息作为历史记录，而是调用历史记录存储插件进行查询获取">
      <change beforePath="$PROJECT_DIR$/plugins/conversation_history/plugin.py" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/conversation_history/plugin.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/conversation_history/sql.py" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/conversation_history/sql.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/dayan_Plugin_keyword_reply/plugin.py" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/dayan_Plugin_keyword_reply/plugin.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/keyword_reply.py" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/keyword_reply.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="HARD" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectId" id="2xf9X0VQMnPmAFrcsGU79WCxqPT" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;last_opened_file_path&quot;: &quot;F:/Downloads/wechat_bot/plugins/blacklist_plugin&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="F:\Downloads\wechat_bot\plugins\blacklist_plugin" />
      <recent name="F:\Downloads\wechat_bot\doc" />
      <recent name="F:\Downloads\wechat_bot" />
    </key>
  </component>
  <component name="RunManager" selected="Python.main">
    <configuration name="main" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="wechat_bot" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/main.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="migrate_conversation_history" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="wechat_bot" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/migrate_conversation_history.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="migrate_simple" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="wechat_bot" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/migrate_simple.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="test_enhanced_keyword_reply" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="wechat_bot" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/test_enhanced_keyword_reply.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="test_human_handover_optimization" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="wechat_bot" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/test_human_handover_optimization.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.main" />
        <item itemvalue="Python.test_enhanced_keyword_reply" />
        <item itemvalue="Python.migrate_simple" />
        <item itemvalue="Python.migrate_conversation_history" />
        <item itemvalue="Python.test_human_handover_optimization" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="8e68c24f-f353-49c5-a1f8-e442f92bb48a" name="Changes" comment="" />
      <created>1748318297796</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748318297796</updated>
      <workItem from="1748318299287" duration="2406000" />
      <workItem from="1748326393252" duration="108000" />
      <workItem from="1748326514341" duration="2832000" />
      <workItem from="1748329627587" duration="6287000" />
      <workItem from="1748336194714" duration="3062000" />
      <workItem from="1748339392248" duration="11976000" />
      <workItem from="1748387463778" duration="1890000" />
      <workItem from="1748423935177" duration="1647000" />
      <workItem from="1748425714994" duration="2598000" />
      <workItem from="1748430201073" duration="1377000" />
      <workItem from="1748431708508" duration="3025000" />
      <workItem from="1748484628234" duration="2901000" />
      <workItem from="1748494870210" duration="5396000" />
      <workItem from="1748505552140" duration="9444000" />
      <workItem from="1748517875106" duration="4487000" />
      <workItem from="1748572367172" duration="20125000" />
      <workItem from="1748852236067" duration="2220000" />
      <workItem from="1748860407123" duration="236000" />
      <workItem from="1748916977992" duration="2635000" />
      <workItem from="1748920043737" duration="2302000" />
    </task>
    <task id="LOCAL-00001" summary="初始版本">
      <created>1748572971326</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1748572971326</updated>
    </task>
    <task id="LOCAL-00002" summary="加入人工服务模块，逻辑bug，历史消息读取只能获取最新来的几条进行判断">
      <created>1748585078053</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1748585078053</updated>
    </task>
    <task id="LOCAL-00003" summary="解决获取用户数据插件连接数据库冲突错误，可以正常获取">
      <created>1748587514238</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1748587514238</updated>
    </task>
    <task id="LOCAL-00004" summary="插件支持消息，链接，图片，卡片">
      <created>1748597863047</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1748597863047</updated>
    </task>
    <task id="LOCAL-00005" summary="插件支持消息，链接，图片，卡片,优化插件定时发送的bug错误">
      <created>1748918710622</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1748918710622</updated>
    </task>
    <task id="LOCAL-00006" summary="优化读取历史记录生成标签逻辑，不是获取新发的几条信息作为历史记录，而是调用历史记录存储插件进行查询获取">
      <created>1748920489045</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1748920489045</updated>
    </task>
    <option name="localTasksCounter" value="7" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="master" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="初始版本" />
    <MESSAGE value="加入人工服务模块，逻辑bug，历史消息读取只能获取最新来的几条进行判断" />
    <MESSAGE value="解决获取用户数据插件连接数据库冲突错误，可以正常获取" />
    <MESSAGE value="插件支持消息，链接，图片，卡片" />
    <MESSAGE value="插件支持消息，链接，图片，卡片,优化插件定时发送的bug错误" />
    <MESSAGE value="优化读取历史记录生成标签逻辑，不是获取新发的几条信息作为历史记录，而是调用历史记录存储插件进行查询获取" />
    <option name="LAST_COMMIT_MESSAGE" value="优化读取历史记录生成标签逻辑，不是获取新发的几条信息作为历史记录，而是调用历史记录存储插件进行查询获取" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/wechat_bot$migrate_simple.coverage" NAME="migrate_simple Coverage Results" MODIFIED="1748921259961" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$final_cleanup.coverage" NAME="final_cleanup Coverage Results" MODIFIED="1748917218518" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$conversation_config_manager.coverage" NAME="conversation_config_manager Coverage Results" MODIFIED="1748504063909" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$test_user_info_db_connection.coverage" NAME="test_user_info_db_connection Coverage Results" MODIFIED="1748587098782" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$simple_message_manager.coverage" NAME="simple_message_manager Coverage Results" MODIFIED="1748918185422" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$work_schedule_simple_manager.coverage" NAME="work_schedule_simple_manager Coverage Results" MODIFIED="1748576493061" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$enhanced_main.coverage" NAME="enhanced_main Coverage Results" MODIFIED="1748588051309" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$main.coverage" NAME="main Coverage Results" MODIFIED="1748922070199" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$auto_hander.coverage" NAME="auto_hander Coverage Results" MODIFIED="1748350677556" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$test_final_segmentation.coverage" NAME="test_final_segmentation Coverage Results" MODIFIED="1748575382615" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$test_work_schedule.coverage" NAME="test_work_schedule Coverage Results" MODIFIED="1748576649122" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$test_mysql_connection_fix.coverage" NAME="test_mysql_connection_fix Coverage Results" MODIFIED="1748586294043" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$test_enhanced_handover.coverage" NAME="test_enhanced_handover Coverage Results" MODIFIED="1748577470374" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$setup_scheduled_messages.coverage" NAME="setup_scheduled_messages Coverage Results" MODIFIED="1748520340092" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$fix_and_add_scheduled_task.coverage" NAME="fix_and_add_scheduled_task Coverage Results" MODIFIED="1748596157274" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$work_schedule_manager.coverage" NAME="work_schedule_manager Coverage Results" MODIFIED="1748576488574" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$test_deepseek_json_fix.coverage" NAME="test_deepseek_json_fix Coverage Results" MODIFIED="1748521478475" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$migrate_scheduled_message_tables.coverage" NAME="migrate_scheduled_message_tables Coverage Results" MODIFIED="1748598629952" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$human_handover_manager.coverage" NAME="human_handover_manager Coverage Results" MODIFIED="1748516978289" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$enhanced_scheduled_message_manager.coverage" NAME="enhanced_scheduled_message_manager Coverage Results" MODIFIED="1748599782685" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$migrate_conversation_history.coverage" NAME="migrate_conversation_history Coverage Results" MODIFIED="1748921161433" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$fix_scheduled_tasks.coverage" NAME="fix_scheduled_tasks Coverage Results" MODIFIED="1748593802479" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$test_enhanced_scheduled_message.coverage" NAME="test_enhanced_scheduled_message Coverage Results" MODIFIED="1748597165007" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$emergency_fix_duplicate.coverage" NAME="emergency_fix_duplicate Coverage Results" MODIFIED="1748599452069" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$test_enhanced_keyword_reply.coverage" NAME="test_enhanced_keyword_reply Coverage Results" MODIFIED="1748922036152" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$test_conversation_plugin.coverage" NAME="test_conversation_plugin Coverage Results" MODIFIED="1748504211477" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$user_data_config_manager.coverage" NAME="user_data_config_manager Coverage Results" MODIFIED="1748587893923" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$test_fixes.coverage" NAME="test_fixes Coverage Results" MODIFIED="1748521160527" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$multimedia_scheduled_message_examples.coverage" NAME="multimedia_scheduled_message_examples Coverage Results" MODIFIED="1748592394478" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$work_schedule_db_manager.coverage" NAME="work_schedule_db_manager Coverage Results" MODIFIED="1748576478687" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$demo_segmentation_modes.coverage" NAME="demo_segmentation_modes Coverage Results" MODIFIED="1748574725705" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$test_work_schedule_db.coverage" NAME="test_work_schedule_db Coverage Results" MODIFIED="1748576504794" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$test_human_handover_optimization.coverage" NAME="test_human_handover_optimization Coverage Results" MODIFIED="1748919517354" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$test_blacklist_plugin.coverage" NAME="test_blacklist_plugin Coverage Results" MODIFIED="1748510503943" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$test_user_data_optimization.coverage" NAME="test_user_data_optimization Coverage Results" MODIFIED="1748518166633" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$scheduled_message_manager.coverage" NAME="scheduled_message_manager Coverage Results" MODIFIED="1748592449251" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$test_long_message_segmentation.coverage" NAME="test_long_message_segmentation Coverage Results" MODIFIED="1748574804177" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$test_message_segmentation.coverage" NAME="test_message_segmentation Coverage Results" MODIFIED="1748573966323" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$test_plugin_sql_mock.coverage" NAME="test_plugin_sql_mock Coverage Results" MODIFIED="1748326943199" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/wechat_bot$send_mixed_now.coverage" NAME="send_mixed_now Coverage Results" MODIFIED="1748595339436" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
</project>