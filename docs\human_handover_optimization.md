# 人工服务插件历史记录获取优化

## 概述

本次优化主要针对人工服务插件的历史记录获取逻辑进行了全面改进，实现了更智能的用户问题分析和上下文总结功能。

## 主要优化内容

### 1. 增强历史记录获取功能

#### 新增SQL函数

- **`get_user_extended_history`**: 获取用户扩展历史记录（支持更长时间范围）
- **`get_user_interaction_patterns`**: 分析用户互动模式
- **`get_user_historical_issues`**: 获取用户历史问题记录

#### 功能特点

- 支持获取30天内的历史记录（可配置）
- 多数据源合并和去重
- 跨时间段的用户行为分析
- 历史问题类型统计

### 2. 智能上下文总结

#### 增强分析维度

1. **用户画像分析**
   - 活跃度评估（高频/活跃/新用户）
   - 互动频率分析
   - 活跃时间偏好

2. **历史问题模式**
   - 问题频率统计
   - 常见问题类型识别
   - 处理状态跟踪

3. **综合处理建议**
   - 基于历史模式的优先级判断
   - 未解决问题跟进提醒
   - 紧急程度评估

### 3. AI增强功能

#### DeepSeek客户端增强

新增 `generate_enhanced_context_summary` 方法，支持：
- 完整用户画像分析
- 深度问题模式识别
- 专业处理建议生成

#### 增强提示词构建

包含以下信息维度：
- 最近对话历史（15条）
- 用户行为模式统计
- 历史问题记录详情
- 当前时间上下文
- 工作时间判断

## 使用示例

### 基础用法

```python
# 获取用户扩展历史记录
extended_history = db.get_user_extended_history(
    user_name="用户名",
    days=30,  # 获取30天内的记录
    limit=50  # 最多50条
)

# 分析用户互动模式
patterns = db.get_user_interaction_patterns(
    user_name="用户名",
    days=30
)

# 获取历史问题记录
historical_issues = db.get_user_historical_issues(
    user_name="用户名", 
    days=90  # 获取90天内的问题记录
)
```

### 增强分析示例

```python
# 生成增强的上下文总结
summary = plugin._generate_context_summary(
    user_name="用户名",
    conversation_history=history,
    trigger_message="我要投诉"
)

# 输出示例：
# "当前问题: 用户表达不满，需要投诉处理；用户特征: 活跃用户，正常互动，工作时间活跃；
#  历史模式: 偶有问题用户，常见问题类型: 故障，有1个待处理问题；
#  处理建议: 存在未解决问题，需要跟进，活跃用户，建议快速响应，问题紧急，需立即处理"
```

## 配置参数

### 推荐配置

```python
config = {
    'history_days': 30,           # 历史记录获取天数
    'history_limit': 50,          # 历史记录条数限制
    'analysis_depth': 'enhanced', # 分析深度
    'ai_enabled': True,           # 启用AI分析
    'priority_keywords': [        # 优先级关键词
        '投诉', '退款', '故障', '紧急'
    ]
}
```

## 数据结构

### 用户互动模式

```python
{
    'total_messages': 45,         # 总消息数
    'active_days': 8,             # 活跃天数
    'avg_messages_per_day': 5.6,  # 日均消息数
    'most_active_hour': 14,       # 最活跃时间
    'message_types': {            # 消息类型分布
        'user': 30,
        'bot': 15
    }
}
```

### 历史问题记录

```python
{
    'id': 1,
    'trigger_keyword': '投诉',
    'trigger_message': '产品有问题',
    'handover_time': datetime,
    'status': 'pending',
    'context_summary': '用户投诉产品质量问题',
    'tags': 'tag1:value1; tag2:value2'
}
```

## 性能优化

1. **数据库查询优化**
   - 使用索引加速查询
   - 限制查询时间范围
   - 合理设置记录数量限制

2. **内存使用优化**
   - 历史记录去重处理
   - 按时间排序优化
   - 及时释放不需要的数据

3. **AI调用优化**
   - 降低API调用频率
   - 使用缓存机制
   - 提供规则分析备选方案

## 监控和日志

### 关键日志

- `📝 从对话记录插件获取到 X 条最近对话`
- `📚 获取到 X 条扩展历史记录`
- `📊 获取用户行为模式: X 个历史问题`
- `✅ 总共获取到 X 条历史对话记录`

### 性能指标

- 历史记录获取耗时
- AI分析成功率
- 用户问题解决率
- 转人工处理效率

## 故障排除

### 常见问题

1. **历史记录获取失败**
   - 检查数据库连接
   - 验证表结构是否正确
   - 确认权限设置

2. **AI分析失败**
   - 检查DeepSeek API配置
   - 验证网络连接
   - 查看API调用限制

3. **性能问题**
   - 调整历史记录获取范围
   - 优化数据库查询
   - 增加缓存机制

## 后续优化方向

1. **机器学习增强**
   - 用户行为预测模型
   - 问题分类自动化
   - 处理效果评估

2. **实时分析**
   - 流式数据处理
   - 实时用户画像更新
   - 动态优先级调整

3. **多维度分析**
   - 情感分析集成
   - 语义相似度计算
   - 知识图谱应用
