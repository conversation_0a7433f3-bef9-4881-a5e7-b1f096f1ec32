"""
定时消息发送插件
支持向指定群聊和私聊用户定时发送固定消息
"""

import threading
import time
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any

from core.plugin_base import Plugin
from .sql import SCHEDULED_MESSAGE_SQL_FUNCTIONS


class ScheduledMessagePlugin(Plugin):
    """
    定时消息发送插件
    支持多种调度类型：一次性、每日、每周、每月、间隔
    """

    priority = 50  # 中等优先级

    def __init__(self, handler):
        super().__init__(handler)

        # 使用主处理程序提供的数据库连接
        self.db = getattr(handler, 'db', None)
        if not self.db:
            raise ValueError("❌ handler 未绑定数据库实例，无法继续")

        # 注册本插件所需的 SQL 函数
        self.db.register_plugin_functions("ScheduledMessagePlugin", SCHEDULED_MESSAGE_SQL_FUNCTIONS)

        # 初始化数据库表
        try:
            self.db.init_scheduled_message_tables()
            self.handler._log("✅ 定时消息表初始化完成")
        except Exception as e:
            self.handler._log(f"❌ 初始化定时消息表失败: {e}", level="ERROR")

        # 启动定时检查线程
        self.running = True
        self.check_interval = 30  # 每30秒检查一次
        self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.scheduler_thread.start()

        self.handler._log("✅ 定时消息插件已启动")

    def __del__(self):
        """插件析构时卸载SQL函数并停止调度器"""
        self.running = False
        if hasattr(self, 'db') and self.db:
            self.db.unregister_plugin_functions("ScheduledMessagePlugin")

    def on_messages(self, chat: str, messages: List[str]) -> Optional[str]:
        """
        定时消息插件不处理用户消息，只负责定时发送
        """
        return None

    def _scheduler_loop(self):
        """调度器主循环"""
        self.handler._log("🕐 定时消息调度器已启动")

        while self.running:
            try:
                self._check_and_send_messages()
            except Exception as e:
                self.handler._log(f"❌ 定时消息检查异常: {e}", level="ERROR")

            # 等待下次检查
            time.sleep(self.check_interval)

        self.handler._log("🕐 定时消息调度器已停止")

    def _check_and_send_messages(self):
        """检查并发送待发送的消息"""
        try:
            pending_messages = self.db.get_pending_messages()

            if not pending_messages:
                return

            self.handler._log(f"📋 发现 {len(pending_messages)} 条待发送消息")

            for message_task in pending_messages:
                try:
                    self._send_scheduled_message(message_task)
                except Exception as e:
                    self.handler._log(f"❌ 发送定时消息失败: {e}", level="ERROR")
                    # 记录发送失败
                    self.db.update_message_sent(
                        message_task['id'],
                        success=False,
                        error_message=str(e)
                    )

        except Exception as e:
            self.handler._log(f"❌ 检查待发送消息失败: {e}", level="ERROR")

    def _send_scheduled_message(self, message_task: Dict):
        """发送单条定时消息（支持多种消息类型）"""
        task_name = message_task['task_name']
        chat_name = message_task['chat_name']
        chat_type = message_task['chat_type']
        message_type = message_task['message_type']
        message_content = message_task['message_content']

        try:
            # 获取微信实例
            wx = getattr(self.handler, 'wx', None)
            if not wx:
                raise Exception("微信实例不可用")

            self.handler._log(f"📤 向{'群聊' if chat_type == 'group' else '私聊'} '{chat_name}' 发送定时消息: {task_name} (类型: {message_type})")

            sent_count = 0
            total_count = 0
            error_messages = []

            if message_type == 'text' and message_content:
                # 简单文本消息（兼容旧版本）
                wx.SendMsg(message_content, who=chat_name)
                sent_count = 1
                total_count = 1

            elif message_type in ['file', 'emotion', 'url_card', 'mixed']:
                # 获取详细内容
                contents = self.db.get_message_contents(message_task['id'])
                total_count = len(contents)

                if not contents:
                    raise Exception("未找到消息内容")

                # 按顺序发送每个内容
                for content in contents:
                    try:
                        # 发送延迟
                        if content['send_delay'] > 0:
                            time.sleep(content['send_delay'])

                        success = self._send_single_content(wx, chat_name, content)
                        if success:
                            sent_count += 1
                        else:
                            error_messages.append(f"发送 {content['content_type']} 内容失败")

                    except Exception as e:
                        error_messages.append(f"发送 {content['content_type']} 内容异常: {str(e)}")
                        self.handler._log(f"⚠️ 发送内容失败: {e}", level="WARNING")

            # 判断发送状态
            if sent_count == total_count:
                status = 'success'
                self.handler._log(f"✅ 定时消息发送成功: {task_name} -> {chat_name} ({sent_count}/{total_count})")
            elif sent_count > 0:
                status = 'partial'
                self.handler._log(f"⚠️ 定时消息部分发送成功: {task_name} -> {chat_name} ({sent_count}/{total_count})", level="WARNING")
            else:
                status = 'failed'
                self.handler._log(f"❌ 定时消息发送失败: {task_name} -> {chat_name}", level="ERROR")

            # 记录发送结果
            self.db.update_message_sent(
                message_task['id'],
                success=(status == 'success'),
                error_message='; '.join(error_messages) if error_messages else None
            )

        except Exception as e:
            self.handler._log(f"❌ 发送定时消息失败 [{task_name}]: {e}", level="ERROR")
            # 记录发送失败
            self.db.update_message_sent(
                message_task['id'],
                success=False,
                error_message=str(e)
            )

    def _send_single_content(self, wx, chat_name: str, content: Dict) -> bool:
        """发送单个内容项"""
        try:
            content_type = content['content_type']

            if content_type == 'text':
                text_content = content['text_content']
                if text_content:
                    wx.SendMsg(text_content, who=chat_name)
                    self.handler._log(f"📝 发送文本: {text_content[:50]}...")
                    return True

            elif content_type == 'file':
                file_paths = content['file_paths']
                if file_paths:
                    wx.SendFiles(file_paths, who=chat_name)
                    self.handler._log(f"📁 发送文件: {len(file_paths)} 个文件")
                    return True

            elif content_type == 'emotion':
                emotion_index = content['emotion_index']
                if emotion_index is not None:
                    wx.SendEmotion(emotion_index, who=chat_name)
                    self.handler._log(f"😊 发送表情: 索引 {emotion_index}")
                    return True

            elif content_type == 'url_card':
                url_content = content['url_content']
                if url_content:
                    wx.SendUrlCard(url_content, [chat_name])
                    self.handler._log(f"🔗 发送链接卡片: {url_content}")
                    return True

            return False

        except Exception as e:
            self.handler._log(f"❌ 发送 {content_type} 内容失败: {e}", level="ERROR")
            return False

    # ==================== 管理接口 ==================== #

    def add_mixed_message(self, task_name: str, chat_name: str, chat_type: str,
                         contents: List[Dict], schedule_type: str, **kwargs) -> bool:
        """
        添加混合类型定时消息

        Args:
            task_name: 任务名称
            chat_name: 聊天窗口名称
            chat_type: 聊天类型 ('group' 或 'private')
            contents: 消息内容列表，格式：
                [
                    {'type': 'text', 'content': '文本内容', 'delay': 0},
                    {'type': 'file', 'paths': ['path1', 'path2'], 'delay': 1},
                    {'type': 'emotion', 'index': 25, 'delay': 0.5},
                    {'type': 'url_card', 'url': 'https://example.com', 'delay': 0}
                ]
            schedule_type: 调度类型 ('daily', 'weekly', 'monthly', 'interval', 'once')
            **kwargs: 其他调度参数
        """
        try:
            message_type = 'mixed' if len(contents) > 1 else contents[0]['type']

            return self.db.add_scheduled_message(
                task_name=task_name,
                chat_name=chat_name,
                chat_type=chat_type,
                message_type=message_type,
                schedule_type=schedule_type,
                contents=contents,
                **kwargs
            )
        except Exception as e:
            self.handler._log(f"❌ 添加混合定时消息失败: {e}", level="ERROR")
            return False

    def add_file_message(self, task_name: str, chat_name: str, chat_type: str,
                        file_paths: List[str], schedule_type: str, **kwargs) -> bool:
        """添加文件定时消息"""
        contents = [{'type': 'file', 'paths': file_paths, 'delay': 0}]
        return self.add_mixed_message(task_name, chat_name, chat_type, contents, schedule_type, **kwargs)

    def add_emotion_message(self, task_name: str, chat_name: str, chat_type: str,
                           emotion_index: int, schedule_type: str, **kwargs) -> bool:
        """添加表情定时消息"""
        contents = [{'type': 'emotion', 'index': emotion_index, 'delay': 0}]
        return self.add_mixed_message(task_name, chat_name, chat_type, contents, schedule_type, **kwargs)

    def add_url_card_message(self, task_name: str, chat_name: str, chat_type: str,
                            url: str, schedule_type: str, **kwargs) -> bool:
        """添加链接卡片定时消息"""
        contents = [{'type': 'url_card', 'url': url, 'delay': 0}]
        return self.add_mixed_message(task_name, chat_name, chat_type, contents, schedule_type, **kwargs)

    def add_daily_message(self, task_name: str, chat_name: str, chat_type: str,
                         message_content: str, send_time: str, max_send_count: int = None) -> bool:
        """
        添加每日定时消息

        Args:
            task_name: 任务名称
            chat_name: 聊天窗口名称
            chat_type: 聊天类型 ('group' 或 'private')
            message_content: 消息内容
            send_time: 发送时间 (格式: "HH:MM", 如 "09:30")
            max_send_count: 最大发送次数 (None表示无限制)
        """
        try:
            from datetime import time
            hour, minute = map(int, send_time.split(':'))
            schedule_time = time(hour, minute)

            return self.db.add_scheduled_message(
                task_name=task_name,
                chat_name=chat_name,
                chat_type=chat_type,
                message_type='text',
                message_content=message_content,
                schedule_type='daily',
                schedule_time=schedule_time,
                max_send_count=max_send_count
            )
        except Exception as e:
            self.handler._log(f"❌ 添加每日定时消息失败: {e}", level="ERROR")
            return False

    def add_weekly_message(self, task_name: str, chat_name: str, chat_type: str,
                          message_content: str, weekday: int, send_time: str,
                          max_send_count: int = None) -> bool:
        """
        添加每周定时消息

        Args:
            weekday: 星期几 (0=周一, 6=周日)
            其他参数同 add_daily_message
        """
        try:
            from datetime import time
            hour, minute = map(int, send_time.split(':'))
            schedule_time = time(hour, minute)

            return self.db.add_scheduled_message(
                task_name=task_name,
                chat_name=chat_name,
                chat_type=chat_type,
                message_type='text',
                message_content=message_content,
                schedule_type='weekly',
                schedule_time=schedule_time,
                schedule_weekday=weekday,
                max_send_count=max_send_count
            )
        except Exception as e:
            self.handler._log(f"❌ 添加每周定时消息失败: {e}", level="ERROR")
            return False

    def add_interval_message(self, task_name: str, chat_name: str, chat_type: str,
                           message_content: str, interval_minutes: int,
                           max_send_count: int = None) -> bool:
        """
        添加间隔定时消息

        Args:
            interval_minutes: 间隔分钟数
            其他参数同 add_daily_message
        """
        try:
            return self.db.add_scheduled_message(
                task_name=task_name,
                chat_name=chat_name,
                chat_type=chat_type,
                message_type='text',
                message_content=message_content,
                schedule_type='interval',
                interval_minutes=interval_minutes,
                max_send_count=max_send_count
            )
        except Exception as e:
            self.handler._log(f"❌ 添加间隔定时消息失败: {e}", level="ERROR")
            return False

    def add_once_message(self, task_name: str, chat_name: str, chat_type: str,
                        message_content: str, send_datetime: str,
                        max_send_count: int = 1) -> bool:
        """
        添加一次性定时消息

        Args:
            send_datetime: 发送时间 (格式: "YYYY-MM-DD HH:MM", 如 "2024-12-25 09:30")
            其他参数同 add_daily_message
        """
        try:
            send_dt = datetime.strptime(send_datetime, "%Y-%m-%d %H:%M")

            return self.db.add_scheduled_message(
                task_name=task_name,
                chat_name=chat_name,
                chat_type=chat_type,
                message_type='text',
                message_content=message_content,
                schedule_type='once',
                schedule_date=send_dt.date(),
                schedule_time=send_dt.time(),
                max_send_count=max_send_count or 1
            )
        except Exception as e:
            self.handler._log(f"❌ 添加一次性定时消息失败: {e}", level="ERROR")
            return False

    def remove_scheduled_message(self, task_name: str) -> bool:
        """删除定时消息任务"""
        try:
            return self.db.delete_scheduled_message(task_name)
        except Exception as e:
            self.handler._log(f"❌ 删除定时消息失败: {e}", level="ERROR")
            return False

    def enable_scheduled_message(self, task_name: str, enabled: bool = True) -> bool:
        """启用/禁用定时消息任务"""
        try:
            return self.db.update_scheduled_message(task_name, enabled=enabled)
        except Exception as e:
            self.handler._log(f"❌ 更新定时消息状态失败: {e}", level="ERROR")
            return False

    def get_all_tasks(self) -> List[Dict]:
        """获取所有定时消息任务"""
        try:
            return self.db.get_all_scheduled_messages()
        except Exception as e:
            self.handler._log(f"❌ 获取定时消息任务失败: {e}", level="ERROR")
            return []

    def get_task_history(self, task_name: str = None, limit: int = 50) -> List[Dict]:
        """获取任务发送历史"""
        try:
            return self.db.get_message_history(task_name, limit)
        except Exception as e:
            self.handler._log(f"❌ 获取发送历史失败: {e}", level="ERROR")
            return []
