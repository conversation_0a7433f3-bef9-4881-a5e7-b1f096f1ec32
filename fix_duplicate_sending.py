#!/usr/bin/env python3
"""
修复重复发送问题的脚本
"""

import sys
import traceback
from datetime import datetime, timedelta
from utils.config_utils import load_config
from db_plugins.mysql import MySQLDB


def fix_duplicate_sending():
    """修复重复发送问题"""
    print("🔧 修复定时消息重复发送问题")
    print("=" * 60)
    
    try:
        # 连接数据库
        config = load_config()
        db_config = config.get("mysql", {})
        db = MySQLDB(**db_config)
        db.connect()
        
        # 注册SQL函数
        from plugins.scheduled_message.sql import SCHEDULED_MESSAGE_SQL_FUNCTIONS
        db.register_plugin_functions("FixDuplicate", SCHEDULED_MESSAGE_SQL_FUNCTIONS)
        
        cursor = db.connection.cursor(dictionary=True)
        
        # 1. 查找所有任务
        print("🔍 查找所有定时任务...")
        cursor.execute("""
            SELECT id, task_name, schedule_type, next_send_at, send_count, 
                   schedule_date, schedule_time, last_sent_at
            FROM scheduled_messages 
            WHERE enabled = TRUE
            ORDER BY task_name
        """)
        
        tasks = cursor.fetchall()
        print(f"📋 找到 {len(tasks)} 个启用的任务")
        
        fixed_count = 0
        disabled_count = 0
        
        # 2. 处理一次性任务
        print("\n🔍 处理一次性任务...")
        for task in tasks:
            if task['schedule_type'] == 'once':
                # 检查一次性任务是否已经发送过
                if task['send_count'] > 0:
                    print(f"  ⚠️ 一次性任务 '{task['task_name']}' 已发送 {task['send_count']} 次，禁用中...")
                    cursor.execute("""
                        UPDATE scheduled_messages 
                        SET enabled = FALSE, next_send_at = NULL 
                        WHERE id = %s
                    """, (task['id'],))
                    disabled_count += 1
                
                # 检查一次性任务是否已过期
                elif task['schedule_date'] and task['schedule_time']:
                    target_time = datetime.combine(task['schedule_date'], task['schedule_time'])
                    if target_time <= datetime.now():
                        print(f"  ⏰ 一次性任务 '{task['task_name']}' 已过期，禁用中...")
                        cursor.execute("""
                            UPDATE scheduled_messages 
                            SET enabled = FALSE, next_send_at = NULL 
                            WHERE id = %s
                        """, (task['id'],))
                        disabled_count += 1
        
        # 3. 重新计算所有任务的下次发送时间
        print("\n🔄 重新计算下次发送时间...")
        for task in tasks:
            if task['schedule_type'] != 'once' or task['send_count'] == 0:
                try:
                    # 使用修复后的函数重新计算
                    new_next_send_at = db.calculate_next_send_time_for_task(task['id'])
                    
                    if new_next_send_at != task['next_send_at']:
                        print(f"  🔧 更新任务 '{task['task_name']}' 的下次发送时间:")
                        print(f"     旧时间: {task['next_send_at']}")
                        print(f"     新时间: {new_next_send_at}")
                        
                        cursor.execute("""
                            UPDATE scheduled_messages 
                            SET next_send_at = %s 
                            WHERE id = %s
                        """, (new_next_send_at, task['id']))
                        fixed_count += 1
                        
                except Exception as e:
                    print(f"  ❌ 计算任务 '{task['task_name']}' 的下次发送时间失败: {e}")
        
        # 4. 提交更改
        db.connection.commit()
        
        # 5. 验证修复结果
        print("\n📊 修复结果:")
        print(f"  - 重新计算时间的任务: {fixed_count}")
        print(f"  - 禁用的过期任务: {disabled_count}")
        
        # 6. 显示当前待发送任务
        print("\n📋 当前待发送任务:")
        pending = db.get_pending_messages()
        if pending:
            for msg in pending:
                print(f"  - {msg['task_name']} -> {msg['chat_name']} (下次: {msg['next_send_at']})")
        else:
            print("  ✅ 没有待发送任务")
        
        # 7. 显示最近发送历史
        print("\n📊 最近发送历史:")
        history = db.get_message_history(limit=10)
        for record in history[-5:]:  # 显示最近5条
            print(f"  - {record['sent_at']}: {record.get('task_name', 'Unknown')} -> {record['status']}")
        
        # 卸载SQL函数
        db.unregister_plugin_functions("FixDuplicate")
        
        print(f"\n✅ 修复完成！")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        traceback.print_exc()
        return False


def create_test_task():
    """创建一个测试任务来验证修复"""
    print("\n🧪 创建测试任务验证修复...")
    
    try:
        # 连接数据库
        config = load_config()
        db_config = config.get("mysql", {})
        db = MySQLDB(**db_config)
        db.connect()
        
        # 注册SQL函数
        from plugins.scheduled_message.sql import SCHEDULED_MESSAGE_SQL_FUNCTIONS
        db.register_plugin_functions("TestFix", SCHEDULED_MESSAGE_SQL_FUNCTIONS)
        
        # 创建一个2分钟后的一次性任务
        from datetime import time
        send_time = datetime.now() + timedelta(minutes=2)
        
        success = db.add_scheduled_message(
            task_name="修复验证测试",
            chat_name="文件传输助手",
            chat_type="private",
            message_type="text",
            message_content="🎉 重复发送问题已修复！这条消息只会发送一次。",
            schedule_type="once",
            schedule_date=send_time.date(),
            schedule_time=send_time.time()
        )
        
        if success:
            print(f"✅ 测试任务创建成功")
            print(f"📅 发送时间: {send_time}")
            print(f"💡 这个任务应该只发送一次，不会重复")
            
            # 检查任务状态
            task = db.get_scheduled_message_by_name("修复验证测试")
            if task:
                print(f"📋 任务状态: 下次发送 {task['next_send_at']}")
        else:
            print("❌ 测试任务创建失败")
        
        # 卸载SQL函数
        db.unregister_plugin_functions("TestFix")
        
        return success
        
    except Exception as e:
        print(f"❌ 创建测试任务失败: {e}")
        traceback.print_exc()
        return False


def monitor_tasks():
    """监控任务状态"""
    print("\n👀 监控任务状态...")
    
    try:
        # 连接数据库
        config = load_config()
        db_config = config.get("mysql", {})
        db = MySQLDB(**db_config)
        db.connect()
        
        cursor = db.connection.cursor(dictionary=True)
        
        # 查看所有启用的任务
        cursor.execute("""
            SELECT task_name, schedule_type, next_send_at, send_count, enabled, last_sent_at
            FROM scheduled_messages 
            ORDER BY next_send_at ASC
        """)
        
        tasks = cursor.fetchall()
        
        print(f"📋 所有任务状态 (共 {len(tasks)} 个):")
        print("-" * 80)
        
        for task in tasks:
            status = "🟢 启用" if task['enabled'] else "🔴 禁用"
            next_send = task['next_send_at'] or "无"
            last_sent = task['last_sent_at'] or "从未发送"
            
            print(f"任务: {task['task_name']}")
            print(f"  类型: {task['schedule_type']} | 状态: {status}")
            print(f"  发送次数: {task['send_count']} | 最后发送: {last_sent}")
            print(f"  下次发送: {next_send}")
            print("-" * 80)
        
        return True
        
    except Exception as e:
        print(f"❌ 监控失败: {e}")
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🔧 定时消息重复发送修复工具")
    print("=" * 60)
    
    success_count = 0
    total_steps = 3
    
    # 1. 修复重复发送问题
    if fix_duplicate_sending():
        success_count += 1
    
    # 2. 创建测试任务
    if create_test_task():
        success_count += 1
    
    # 3. 监控任务状态
    if monitor_tasks():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 修复结果: {success_count}/{total_steps} 步骤成功")
    
    if success_count == total_steps:
        print("🎉 重复发送问题修复成功！")
        print("\n💡 修复内容:")
        print("- ✅ 修复了一次性任务重复发送的问题")
        print("- ✅ 禁用了已过期的一次性任务")
        print("- ✅ 重新计算了所有任务的下次发送时间")
        print("- ✅ 创建了测试任务验证修复效果")
        
        print("\n🚀 现在定时消息将按预期工作:")
        print("- 📅 每日任务：每天指定时间发送一次")
        print("- 📆 每周任务：每周指定时间发送一次")
        print("- ⏰ 一次性任务：只发送一次，发送后自动禁用")
        print("- 🔄 间隔任务：按指定间隔重复发送")
        
        print("\n⚠️ 请观察接下来几分钟，确认不再有重复发送")
        return True
    else:
        print("⚠️ 部分修复步骤失败，请检查错误信息")
        return False


if __name__ == "__main__":
    main()
